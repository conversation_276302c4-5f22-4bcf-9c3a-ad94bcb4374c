"""CLI para rodar testes do Conversas.AI"""
import argparse
import os
import sys
import logging
import pytest

logger = logging.getLogger("conversas_logger")

sys.path.append(os.path.join(os.path.dirname(__file__), ".."))

# GCP Config
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
os.environ['GCP_BIGQUERY_KEY_ID'] = "50a86fe67d5acdaca6ab2faf80e28dd48983f8d7"
os.environ['GCP_BIGQUERY_CLIENT_ID'] = "100993773084168766446"
os.environ['GCP_BIGQUERY_CERT_URL'] = "https://www.googleapis.com/robot/v1/metadata/x509/conversas-ai-dev%40conversas-ai.iam.gserviceaccount.com"
os.environ['GCP_BIGQUERY_PROJECT_ID'] = "conversas-ai"
os.environ['GCP_BIGQUERY_DATASET'] = "development"

#Postgres Config
#TODO: analisar como criar essa configuração. Já segue um esboço
os.environ["POSTGRES_HOST"] = "postgres"
os.environ["POSTGRES_PORT"] = "5432"
os.environ["POSTGRES_USER"] = "postgres"
os.environ["POSTGRES_PASSWORD"] = "postgres"
os.environ["POSTGRES_DB"] = "orion"

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run tests for Conversas.AI")
    parser.add_argument("--unit", "-u", action="store_true", help="Run unit tests")
    parser.add_argument("--integration", "-i", action="store_true", help="Run integration tests")
    parser.add_argument("--workers", "-w", action="store_true", help="Run Worker tests")
    parser.add_argument("--api", "-a", action="store_true", help="Run API tests")
    parser.add_argument(
        "--functionalities",
        "-f",
        action="store_true",
        help="Run functionalities tests"
        )
    parser.add_argument("--verbose", "-v", action="store_true", help="Run tests in verbose mode")
    parser.add_argument("--coverage", "-c", action="store_true", help="Run with coverage")
    args = parser.parse_args()
    EXIT_CODE = 0

    test_args = []

    if args.unit:
        if args.workers:
            os.environ["MODE"] = "worker"
            test_args = [
                "tests/unit/test_bigquery_data.py",
                "tests/unit/test_openai_response_module.py",
                "tests/unit/test_workers.py"
            ]
        if args.functionalities:
            os.environ["MODE"] = "worker"
            test_args.append("tests/unit/test_functionalities.py")

        if args.api:
            os.environ["MODE"] = "api"
            os.environ['API_MASTER_KEY'] = "my_precious"
            os.environ['ALLOWED_ORIGINS'] = "https://api.z-api.io"
            os.environ['RATE_LIMIT_ENABLED'] = "false"
            logger.warning("Running API tests...")
            test_args.append("tests/unit/test_api.py")

        if args.verbose:
            test_args.append("-vv -s")

    if args.integration:
        logger.warning("Integration tests: NotImplemented")

    if args.coverage:
        test_args.extend(["--junitxml", "test_results/report.xml"])
        coverage_command = ["coverage", "run", "--source=src", "-m", "pytest"] + test_args
        EXIT_CODE = os.system(" ".join(coverage_command))

        os.system("coverage report -m")
        os.system("coverage xml -o test_results/coverage.xml")

        print("Coverage report moved to test_results/coverage.xml")

    elif test_args:

        print("Coverage report moved to test_results/report.xml")

        EXIT_CODE = pytest.main(test_args)

    if not any(vars(args).values()):
        parser.print_help()

    if EXIT_CODE == 0:
        print(f"Tests for {', '.join(test_args)} succeeded!")
    else:
        print(f"Tests for {', '.join(test_args)} failed!")

    print(f'Finalizado com exit_code {EXIT_CODE}')

