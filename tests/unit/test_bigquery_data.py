import os
import pandas as pd
from pandas.testing import assert_frame_equal
import pytest
import json
from unittest.mock import patch, MagicMock, call, ANY
from psycopg2 import sql

from src.data.bigquery_data import (
    get_from_empresa,
    get_from_instance,
    redis_client_get,
    redis_client_set,
    BigQueryData
)

GCP_BIGQUERY_DATASET = os.getenv("GCP_BIGQUERY_DATASET")
DB_SCHEMA = os.getenv("DB_SCHEMA", "public")
BUCKET_NAME_CAMPANHA = os.getenv("BUCKET_NAME_CAMPANHA", "image_campanha")

@pytest.fixture
def mock_connections():
    with patch('src.data.bigquery_data._postgres_handler.connections') as mock:
        yield mock

@pytest.fixture
def mock_logger():
    with patch('src.data.bigquery_data.logger') as mock:
        yield mock

@pytest.fixture
def mock_redis_client():
    with patch('src.data.bigquery_data._postgres_handler.connections.redis_client') as mock:
        yield mock


class TestBigQueryData:
    def setup_class(self):
        self.bq_data = BigQueryData("1")

    def test_get_from_empresa_cache_hit(self, mock_redis_client):
        dados_em_cache = {
            "instance_id": "123",
            "token": "abc"
        }
        mock_redis_client.get.return_value = json.dumps(dados_em_cache)

        instance_id, token = get_from_empresa("1")

        assert instance_id == "123"
        assert token == "abc"
        
        mock_redis_client.get.assert_called_once_with("instances:1")

    def test_get_from_empresa_cache_miss(self, mocker):
        from src.data.bigquery_data._postgres_handler import get_from_empresa

        mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_get', return_value=None)
        mock_redis_set = mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_set')
        
        mock_cursor = MagicMock()
        mock_cursor.fetchone.return_value = {"instance_id": "db-123", "token": "db-abc"}
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        with patch('src.data.bigquery_data._postgres_handler.connections.postgres_connection.getconn') as mock_getconn:
            mock_getconn.return_value = mock_conn

            instance_id, token = get_from_empresa("1")

            assert instance_id == "db-123"
            assert token == "db-abc"

            expected_query = f"SELECT instance_id, token FROM {DB_SCHEMA}.instances WHERE id_empresa = %s"
            mock_cursor.execute.assert_called_once_with(expected_query, ("1",))
            mock_redis_set.assert_called_once()

    def test_get_from_instance_cache_hit(self, mock_redis_client):
        dados_em_cache = {
            "id_empresa": "1",
            "token": "abc",
        }
        mock_redis_client.get.return_value = json.dumps(dados_em_cache)

        id_empresa, token = get_from_instance("1")

        assert id_empresa == "1"
        assert token == "abc"

        mock_redis_client.get.assert_called_once_with("empresas:1")

    def test_get_from_instance_cache_miss(self, mocker):
        from src.data.bigquery_data._postgres_handler import get_from_instance

        mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_get', return_value=None)
        mock_redis_set = mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_set')

        mock_cursor = MagicMock()
        mock_cursor.fetchone.return_value = {"id_empresa": "db-1", "token": "db-abc"}
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        with patch('src.data.bigquery_data._postgres_handler.connections.postgres_connection.getconn') as mock_getconn:
            mock_getconn.return_value = mock_conn

            id_empresa, token = get_from_instance("instance-123")

            assert id_empresa == "db-1"
            assert token == "db-abc"

            expected_query = f"SELECT id_empresa, token FROM {DB_SCHEMA}.instances WHERE instance_id = %s"
            mock_cursor.execute.assert_called_once_with(expected_query, ("instance-123",))
            mock_redis_set.assert_called_once()

    def test_redis_client(self, mock_redis_client):
        def test_redis_client_get():
            mock_redis_client.get.return_value = "value"
            assert redis_client_get("key") == "value"
        
        def test_redis_client_set():
            redis_client_set("key", "value")
            mock_redis_client.set.assert_called_with("key", "value", ex=None)
            assert redis_client_get("key") == "value"

        test_redis_client_get()
        test_redis_client_set()

    @patch('src.data.bigquery_data._postgres_handler.connections.redis_client')
    def test_get_gym_context_cache_hit(self, mock_redis_client):
        # Configura o mock para simular cache hit
        mock_redis_client.get.return_value = json.dumps({"context": "gym"})
        
        # Chama o método a ser testado
        result = self.bq_data.get_gym_context()
        
        # Verifica o resultado
        assert result == {"context": "gym"}
        
        # Verifica se o Redis foi chamado corretamente
        mock_redis_client.get.assert_called_once_with("gym_context-1")

    def test_get_gym_context_cache_miss(self, mocker):
        mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_get', return_value=None)
        mock_redis_set = mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_set')

        mock_execute = mocker.patch.object(
            self.bq_data,
            '_execute_query'
        )
        
        mock_result_obj = MagicMock()
        contexto_str = json.dumps({"context": "gym"})
        mock_result_obj.to_dataframe.return_value = pd.DataFrame(
            [{'contexto_academia_json': contexto_str}]
        )
        mock_execute.return_value = mock_result_obj

        context = self.bq_data.get_gym_context()

        assert context == {"context": "gym"}

        expected_query = f"SELECT contexto_academia_json FROM {DB_SCHEMA}.contexto_academia"
        mock_execute.assert_called_once_with(expected_query)

        mock_redis_set.assert_called_once_with(
            f"gym_context-{self.bq_data.id_empresa}", 
            contexto_str,
            ex=28800
        )
        
    def test_update_gym_context(self, mocker, mock_redis_client):
        table_id = f"{DB_SCHEMA}.contexto_academia"
        mocker.patch.object(
            self.bq_data,
            '_check_if_row_exists',
            return_value=True
        )
        mock_cursor = MagicMock()
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(
            self.bq_data,
            '_get_conn',
            return_value=mock_conn
        )
        context_data = {"context": "gym"}
        contexto_json = json.dumps(context_data)
        self.bq_data.update_gym_context(context_data)

        expected_query = f"""
                    UPDATE {table_id}
                    SET contexto_academia_json = %s,
                        data_ultima_atualizacao = %s
                    WHERE id_empresa = %s
                """
        expected_params = (contexto_json, ANY, self.bq_data.id_empresa)

        mock_cursor.execute.assert_called_once_with(
            expected_query,
            expected_params
        )
        mock_conn.commit.assert_called_once()
        mock_redis_client.set.assert_called_with(
            "gym_context-1", 
            contexto_json, 
            ex=28800
        )

    def test_insert_gym_context(self, mocker, mock_redis_client):
        table_id = f"{DB_SCHEMA}.contexto_academia"
        mocker.patch.object(
            self.bq_data,
            '_check_if_row_exists',
            return_value=False
        )
        mock_cursor = MagicMock()
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(
            self.bq_data,
            '_get_conn',
            return_value=mock_conn
        )
        context_data = {"context": "gym"}
        contexto_json = json.dumps(context_data)
        self.bq_data.update_gym_context(context_data)

        expected_query = f"""
                    INSERT INTO {table_id} (contexto_academia_json, data_ultima_atualizacao, id_empresa)
                    VALUES (%s, %s, %s)
                """
        expected_params = (contexto_json, ANY, self.bq_data.id_empresa)

        mock_cursor.execute.assert_called_once_with(
            expected_query,
            expected_params,
        )
        mock_conn.commit.assert_called_once()
        mock_redis_client.set.assert_called_with(
            "gym_context-1", 
            contexto_json,
            ex=28800
        )

    @patch('src.data.bigquery_data._postgres_handler.connections.redis_client')
    def test_get_plans_context_cache_hit(self, mock_redis_client):
        # Configura o mock para simular cache hit
        mock_redis_client.get.return_value = json.dumps({"plans": []})

        # Chama o método a ser testado
        result = self.bq_data.get_plans_context()

        # Verifica o resultado
        assert result == {"plans": []}

        # Verifica se o Redis foi chamado corretamente
        mock_redis_client.get.assert_called_once_with("plans_context-1")

    def test_get_plans_context_cache_miss(self, mocker, mock_redis_client):
        mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_get', return_value=None)
        mock_redis_set = mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_set')

        mock_execute = mocker.patch.object(self.bq_data, '_execute_query')
        
        mock_result_obj = MagicMock()
        contexto_str = json.dumps({"plans": []})
        mock_result_obj.to_dataframe.return_value = pd.DataFrame(
            [{'planos_json': contexto_str}]
        )
        mock_execute.return_value = mock_result_obj

        context_plans = self.bq_data.get_plans_context()

        assert context_plans == {"plans": []}

        expected_query = f"SELECT * FROM {DB_SCHEMA}.contexto_planos"
        mock_execute.assert_called_once_with(expected_query)

        mock_redis_set.assert_called_once_with(
            f"plans_context-{self.bq_data.id_empresa}", 
            contexto_str,
            ex=28800
        )

    def test_update_plans_context(self, mocker, mock_redis_client):
        table_id = f"{DB_SCHEMA}.contexto_planos"
        mocker.patch.object(
            self.bq_data,
            '_check_if_row_exists',
            return_value=True,
        )
        mock_cursor = MagicMock()
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(
            self.bq_data,
            '_get_conn',
            return_value=mock_conn
        )

        context_data = {"plans": []}
        contexto_json = json.dumps(context_data)
        self.bq_data.update_plans_context(context_data)

        expected_query = f"UPDATE {table_id} SET planos_json = %s, data_ultima_atualizacao = %s WHERE id_empresa = %s"
        expected_params = [contexto_json, ANY, self.bq_data.id_empresa]

        mock_cursor.execute.assert_called_once_with(expected_query, expected_params)
        mock_conn.commit.assert_called_once()
        mock_redis_client.set.assert_called_with(
            "plans_context-1",
            contexto_json,
            ex=28800
        )

    def test_insert_plans_context(self, mocker, mock_redis_client):
        table_id = f"{DB_SCHEMA}.contexto_planos"
        mocker.patch.object(
            self.bq_data,
            '_check_if_row_exists',
            return_value=False,
        )
        mock_cursor = MagicMock()
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(
            self.bq_data,
            '_get_conn',
            return_value=mock_conn
        )

        context_data = {"plans": []}
        contexto_json = json.dumps(context_data)
        self.bq_data.update_plans_context(context_data)

        expected_query = f"INSERT INTO {table_id} (planos_json, data_ultima_atualizacao, id_empresa) VALUES (%s, %s, %s)"
        expected_params = [contexto_json, ANY, self.bq_data.id_empresa]

        mock_cursor.execute.assert_called_once_with(expected_query, expected_params)
        mock_conn.commit.assert_called_once()
        mock_redis_client.set.assert_called_with(
            "plans_context-1", 
            contexto_json, 
            ex=28800
        )

    @patch('src.data.bigquery_data._postgres_handler.connections.redis_client')
    def test_get_phase_context_cache_hit(self, mock_redis_client):
        # Configura o mock para simular cache hit
        mock_phase_context = [
            {
                "nome_fase": "Leads Hoje",
                "descricao_fase": "Leads Hoje",
                "instrucao_ia_fase": "Instrução"
            }
        ]
        mock_redis_client.get.return_value = json.dumps(mock_phase_context)

        # Chama o método a ser testado
        result = self.bq_data.get_phase_context()

        # Verifica o resultado
        expected_result = mock_phase_context[0]
        assert result == expected_result

        # Verifica se o Redis foi chamado corretamente
        mock_redis_client.get.assert_called_once_with("phases_context-1")

    def test_get_phase_context_cache_miss_default(self, mocker):
        mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_get', return_value=None)
        mock_redis_set = mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_set')

        mock_cursor = MagicMock()
        mock_db_results = [
            {"nome_fase": "Leads Hoje", "descricao_fase": "Desc A", "instrucao_ia_fase": "Inst A"},
            {"nome_fase": "Clientes", "descricao_fase": "Desc B", "instrucao_ia_fase": "Inst B"}
        ]
        mock_cursor.fetchall.return_value = mock_db_results
        
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(self.bq_data, '_get_conn', return_value=mock_conn)
        
        result = self.bq_data.get_phase_context()

        assert result == mock_db_results[0]

        expected_query = f"SELECT descricao_fase, instrucao_ia_fase, nome_fase FROM {DB_SCHEMA}.contexto_fases LIMIT 1"
        mock_cursor.execute.assert_called_once_with(expected_query, [])

        expected_cache_value = json.dumps(mock_db_results)
        mock_redis_set.assert_called_once_with(
            f"phases_context-{self.bq_data.id_empresa}",
            expected_cache_value,
            ex=28800
        )

    #FIXME: este teste não está passando, então decidi pular ele e ir para os outros, para não perder tempo
    def test_update_phases_context(self, mocker, mock_redis_client):
        mocker.patch.object(
            self.bq_data,
            '_check_if_row_exists',
            side_effect=[True, True]
        )
        mock_cursor, mock_conn = MagicMock(), MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(
            self.bq_data,
            '_get_conn',
            return_value=mock_conn)
        mocker.patch(
            'src.data.bigquery_data._postgres_handler.redis_client_get',
            return_value=None
        )

        context = [
            {"codigo": 1, "name": "Leads Hoje", "descricao": "Desc A", "instrucao_ia": "Inst A"},
            {"codigo": 2, "name": "Clientes Ativos", "descricao": "Desc B", "instrucao_ia": "Inst B"}
        ]

        self.bq_data.update_phases_context(context)

        query_template = f"""
                    UPDATE {DB_SCHEMA}.contexto_fases 
                    SET 
                    codigo_fase = %s,
                    nome_fase = %s,
                    descricao_fase = %s,
                    instrucao_ia_fase = %s,
                    data_ultima_atualizacao = %s
                    WHERE nome_fase = %s AND id_empresa = %s"""

        expected_calls = [
            call(query_template, (1, "Leads Hoje", "Desc A", "Inst A", ANY, "Leads Hoje", self.bq_data.id_empresa)),
            call(query_template, (2, "Clientes Ativos", "Desc B", "Inst B", ANY, "Clientes Ativos", self.bq_data.id_empresa))
        ]

        mock_cursor.execute.assert_has_calls(expected_calls, any_order=True)
        mock_conn.commit.assert_called_once()
        
        mock_redis_client.set.assert_called_once()

    def test_insert_phases_context(self, mocker, mock_redis_client):
        mocker.patch.object(
            self.bq_data,
            '_check_if_row_exists',
            side_effect=[False, False]
        )
        mock_cursor, mock_conn = MagicMock(), MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(
            self.bq_data,
            '_get_conn',
            return_value=mock_conn
        )
        mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_get', return_value=None)

        context = [
            {"codigo": 1, "name": "Leads Novos", "descricao": "Desc C", "instrucao_ia": "Inst C"}
        ]

        self.bq_data.update_phases_context(context)

        query_template = f"INSERT INTO {DB_SCHEMA}.contexto_fases (codigo_fase, nome_fase, descricao_fase, instrucao_ia_fase, data_ultima_atualizacao, id_empresa) VALUES (%s, %s, %s, %s, %s, %s)"
        expected_params = (1, "Leads Novos", "Desc C", "Inst C", ANY, self.bq_data.id_empresa)
        
        mock_cursor.execute.assert_called_once_with(
            query_template,
            expected_params
        )
        mock_conn.commit.assert_called_once()
        mock_redis_client.set.assert_called_once()

    @patch('src.data.bigquery_data._postgres_handler.connections.redis_client')
    def test_get_classes_context_cache_hit(self, mock_redis_client):
        # Simula cache hit
        mock_redis_client.get.return_value = json.dumps({"classes": []})

        # Chama o método a ser testado
        result = self.bq_data.get_classes_context()

        # Verifica o resultado
        assert result == {"classes": []}

        # Verifica se o Redis foi chamado corretamente
        mock_redis_client.get.assert_called_once_with("classes_context-1")

    def test_get_classes_context_cache_miss(self, mocker):
        mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_get', return_value=None)
        mock_redis_set = mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_set')

        mock_execute = mocker.patch.object(
            self.bq_data,
            '_execute_query'
        )
        
        mock_result_obj = MagicMock()
        contexto_str = json.dumps({"classes": []})
        mock_result_obj.to_dataframe.return_value = pd.DataFrame(
            [{'turmas': contexto_str}]
        )
        mock_execute.return_value = mock_result_obj

        context = self.bq_data.get_classes_context()

        assert context == {"classes": []}

        expected_query = f"SELECT * FROM {DB_SCHEMA}.contexto_turmas"
        mock_execute.assert_called_once_with(expected_query)

        mock_redis_set.assert_called_once_with(
            f"classes_context-{self.bq_data.id_empresa}", 
            contexto_str,
            ex=28800
        )

    def test_update_classes_context(self, mocker, mock_redis_client):
        table_id = f"{DB_SCHEMA}.contexto_turmas"
        mocker.patch.object(
            self.bq_data,
            '_check_if_row_exists',
            return_value=True,
        )
        mock_cursor = MagicMock()
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(
            self.bq_data,
            '_get_conn',
            return_value=mock_conn
        )
        context_data = {"classes": []}
        contexto_json = json.dumps(context_data)
        self.bq_data.update_classes_context(context_data)

        expected_query = f"UPDATE {table_id} SET turmas = %s, data_ultima_atualizacao = %s WHERE id_empresa = %s"
        expected_params = (contexto_json, ANY, self.bq_data.id_empresa)

        mock_cursor.execute.assert_called_once_with(
            expected_query,
            expected_params
        )
        mock_conn.commit.assert_called_once()
        mock_redis_client.set.assert_called_with(
            "classes_context-1", 
            contexto_json, 
            ex=28800
        )

    def test_insert_classes_context(self, mocker, mock_redis_client):
        table_id = f"{DB_SCHEMA}.contexto_turmas"
        mocker.patch.object(
            self.bq_data,
            '_check_if_row_exists',
            return_value=False,
        )
        mock_cursor = MagicMock()
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(
            self.bq_data,
            '_get_conn',
            return_value=mock_conn
        )
        context_data = {"classes": []}
        contexto_json = json.dumps(context_data)
        self.bq_data.update_classes_context(context_data)

        expected_query = f"INSERT INTO {table_id} (turmas, data_ultima_atualizacao, id_empresa) VALUES (%s, %s, %s)"
        expected_params = (contexto_json, ANY, self.bq_data.id_empresa)

        mock_cursor.execute.assert_called_once_with(
            expected_query,
            expected_params,
        )
        mock_conn.commit.assert_called_once()
        mock_redis_client.set.assert_called_with(
            "classes_context-1", 
            contexto_json, 
            ex=28800
        )

    @patch('src.data.bigquery_data._postgres_handler.connections.redis_client')
    def test_get_products_context_cache_hit(self, mock_redis_client):
        # Simula cache hit
        mock_products = {"products": []}
        mock_redis_client.get.return_value = json.dumps(mock_products)

        # Chama o método a ser testado
        result = self.bq_data.get_products_context()

        # Verifica o resultado
        assert result == mock_products

        # Verifica se o Redis foi chamado corretamente
        mock_redis_client.get.assert_called_once_with("products_context-1")
    
    def test_get_products_context_cache_miss(self, mocker):
        mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_get', return_value=None)
        mock_redis_set = mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_set')

        mock_execute = mocker.patch.object(
            self.bq_data,
            '_execute_query'
        )
        
        mock_result_obj = MagicMock()
        contexto_str = json.dumps({"products": []})
        mock_result_obj.to_dataframe.return_value = pd.DataFrame(
            [{'produtos': contexto_str}]
        )
        mock_execute.return_value = mock_result_obj

        context = self.bq_data.get_products_context()

        assert context == {"products": []}

        expected_query = f"SELECT * FROM {DB_SCHEMA}.contexto_produtos"
        mock_execute.assert_called_once_with(expected_query)

        mock_redis_set.assert_called_once_with(
            f"products_context-{self.bq_data.id_empresa}", 
            contexto_str,
            ex=28800
        )

    def test_update_products_context(self, mocker, mock_redis_client):
        table_id = f"{DB_SCHEMA}.contexto_produtos"
        mocker.patch.object(
            self.bq_data,
            '_check_if_row_exists',
            return_value=True
        )
        mock_cursor = MagicMock()
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(
            self.bq_data,
            '_get_conn',
            return_value=mock_conn
        )
        context_data = {"products": ["produto A", "produto B"]}
        contexto_json = json.dumps(context_data)
        self.bq_data.update_products_context(context_data)

        expected_query = f"UPDATE {table_id} SET produtos = %s, data_ultima_atualizacao = %s WHERE id_empresa = %s"
        expected_params = (contexto_json, ANY, self.bq_data.id_empresa)

        mock_cursor.execute.assert_called_once_with(expected_query, expected_params)
        mock_conn.commit.assert_called_once()
        mock_redis_client.set.assert_called_with(
            "products_context-1",
            contexto_json,
            ex=28800,
        )

    def test_insert_products_context(self, mocker, mock_redis_client):
        table_id = f"{DB_SCHEMA}.contexto_produtos"
        mocker.patch.object(
            self.bq_data,
            '_check_if_row_exists',
            return_value=False,
        )
        mock_cursor = MagicMock()
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(
            self.bq_data,
            '_get_conn',
            return_value=mock_conn
        )

        context_data = {"products": ["produto C"]}
        contexto_json = json.dumps(context_data)
        self.bq_data.update_products_context(context_data)

        expected_query = f"INSERT INTO {table_id} (produtos, data_ultima_atualizacao, id_empresa) VALUES (%s, %s, %s)"
        expected_params = (contexto_json, ANY, self.bq_data.id_empresa)

        mock_cursor.execute.assert_called_once_with(expected_query, expected_params)
        mock_conn.commit.assert_called_once()
        mock_redis_client.set.assert_called_with(
            "products_context-1", 
            contexto_json, 
            ex=28800
        )

    @patch('src.data.bigquery_data._postgres_handler.connections.redis_client')
    def test_get_user_context_cache_hit(self, mock_redis_client):
        telefone = "123456789"
        # Simula cache hit
        mock_user_context = {"user": {}, "fase_atual": "LEADS_HOJE"}
        mock_redis_client.get.return_value = json.dumps(mock_user_context)

        # Chama o método
        result = self.bq_data.get_user_context(telefone)

        # Verifica o resultado
        expected_result = (mock_user_context, "LEADS_HOJE")
        assert result == expected_result

        # Verifica se o Redis foi chamado
        mock_redis_client.get.assert_called_once_with(f"{telefone}-{self.bq_data.id_empresa}")

    def test_get_user_context_cache_miss(self, mocker):
        telefone = "123456789"
        mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_get', return_value=None)
        mock_redis_set = mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_set')
        mock_execute = mocker.patch.object(self.bq_data, '_execute_query')
        
        dados_do_banco = {
            "user": {"nome": "joão"},
            "fase_crm": "QUALIFICACAO" # simulamos um dado
        }
        df_from_db = pd.DataFrame([
            {'contexto_usuario_json': json.dumps(dados_do_banco)}
        ])
        
        mock_result_obj = MagicMock()
        mock_result_obj.to_dataframe.return_value = df_from_db
        mock_execute.return_value = mock_result_obj

        contexto_retornado, fase_retornada = self.bq_data.get_user_context(telefone)

        expected_query = f"SELECT * FROM {DB_SCHEMA}.contexto_usuario WHERE telefone = %s"
        expected_params = [telefone]
        mock_execute.assert_called_once_with(
            query=expected_query,
            extra_query="",
            use_id_empresa=True,
            params=expected_params
        )

        contexto_esperado = dados_do_banco.copy()
        contexto_esperado["fase_atual"] = "QUALIFICACAO"
        contexto_esperado.pop("fase_crm", None) 
        
        assert fase_retornada == "QUALIFICACAO"
        assert contexto_retornado == contexto_esperado

        cache_key = f"{telefone}-{self.bq_data.id_empresa}"
        mock_redis_set.assert_called_once_with(
            cache_key,
            json.dumps(contexto_esperado),
            ex=28800
        )

    def test_save_user_context(self, mocker):
        mocker.patch.object(self.bq_data, '_check_if_row_exists', return_value=False)
        mock_cursor, mock_conn = MagicMock(), MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(self.bq_data, '_get_conn', return_value=mock_conn)
        mock_redis_set = mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_set')

        context = {"user": {"name": "joão"}}
        telefone = "123456789"
        fase = "LEADS_HOJE"

        self.bq_data.save_user_context(context, telefone, fase, origin="test_unit")

        expected_query = """
                    INSERT INTO f"{DB_SCHEMA}.contexto_usuario" 
                    (id_usuario_unico, id_usuario, contexto_usuario_json, data_ultima_atualizacao, telefone, id_empresa, fase_atual, id_matriz) 
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """
        mock_cursor.execute.assert_called_once() # a query é complexa, apenas checar a chamada é suficiente aqui
        mock_conn.commit.assert_called_once()

        expected_cache_context = {"origin_last_update": "test_unit", "user": {"name": "joão"}, "fase_atual": fase}
        mock_redis_set.assert_called_once_with(
            f"{telefone}-{self.bq_data.id_empresa}", 
            json.dumps(expected_cache_context), 
            ex=28800
    )

    def test_get_personality_context_cache_hit(self, mocker):
        personalidade_cache = {"personalidade": "Voz feminina sempre."}
        mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_get', return_value=json.dumps(personalidade_cache))
        mock_execute = mocker.patch.object(self.bq_data, '_execute_query')

        result = self.bq_data.get_personality_context()

        assert result == "Voz feminina sempre."
        mock_execute.assert_not_called()

    def test_get_personality_context_cache_miss(self, mocker):
        mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_get', return_value=None)
        mock_redis_set = mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_set')
        mock_execute = mocker.patch.object(self.bq_data, '_execute_query')
        
        df_from_db = pd.DataFrame({'personalidade': ["Voz do banco de dados."]})
        mock_result_obj = MagicMock()
        mock_result_obj.to_dataframe.return_value = df_from_db
        mock_execute.return_value = mock_result_obj

        result = self.bq_data.get_personality_context()

        assert result == "Voz do banco de dados."
        expected_query = f"SELECT personalidade FROM {DB_SCHEMA}.contexto_personalidade"
        mock_execute.assert_called_once_with(expected_query)
        mock_redis_set.assert_called_once_with(f"personality_context-{self.bq_data.id_empresa}", json.dumps({"personalidade": "Voz do banco de dados."}), ex=28800)

    def test_update_personality_context(self, mocker, mock_redis_client):
        table_id = f'"{DB_SCHEMA}"."contexto_personalidade"'
        mocker.patch.object(
            self.bq_data,
            '_check_if_row_exists',
            return_value=True
        )
        mock_cursor = MagicMock()
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(
            self.bq_data,
            '_get_conn',
            return_value=mock_conn
        )

        context_data = {"personalidade": "Voz calma e amigável."}
        personalidade_json = json.dumps(context_data.get("personalidade"))

        self.bq_data.update_personality_context(context_data)

        expected_query = f"UPDATE {table_id} SET personalidade = %s, data_ultima_atualizacao = %s WHERE id_empresa = %s"
        expected_params = [personalidade_json, ANY, self.bq_data.id_empresa]
        mock_cursor.execute.assert_called_once_with(expected_query, expected_params)
        mock_conn.commit.assert_called_once()
        
        mock_redis_client.set.assert_called_with(
            f"personality_context-{self.bq_data.id_empresa}",
            json.dumps(context_data), 
            ex=28800
        )

    def test_insert_personality_context(self, mocker, mock_redis_client):
        table_id = f'"{DB_SCHEMA}"."contexto_personalidade"'
        mocker.patch.object(
            self.bq_data,
            '_check_if_row_exists',
            return_value=False
        )
        mock_cursor = MagicMock()
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(
            self.bq_data,
            '_get_conn',
            return_value=mock_conn
        )

        context_data = {"personalidade": "Voz formal e objetiva."}
        personalidade_json = json.dumps(context_data.get("personalidade"))

        self.bq_data.update_personality_context(context_data)

        expected_query = f"INSERT INTO {table_id} (personalidade, data_ultima_atualizacao, id_empresa) VALUES (%s, %s, %s)"
        expected_params = [personalidade_json, ANY, self.bq_data.id_empresa]
        mock_cursor.execute.assert_called_once_with(expected_query, expected_params)
        mock_conn.commit.assert_called_once()
        
        mock_redis_client.set.assert_called_with(
            f"personality_context-{self.bq_data.id_empresa}",
            json.dumps(context_data),
            ex=28800
        )

    def test_save_message(self, mocker):
        mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_get', return_value=None) # simula sem conversa atual
        mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_set')
        mock_cursor, mock_conn = MagicMock(), MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(self.bq_data, '_get_conn', return_value=mock_conn)

        self.bq_data.save_message("user", "Hello", "123456789")

        mock_cursor.execute.assert_called_once()
        mock_conn.commit.assert_called_once()

    @patch('src.data.bigquery_data._postgres_handler.connections.redis_client')
    def test_get_last_messages_cache_hit(self, mock_redis_client):
        telefone = "123456789"
        # Simula cache hit
        mock_messages = [{"message": "Hello"}]
        mock_redis_client.get.return_value = json.dumps(mock_messages)

        # Chama o método
        result = self.bq_data.get_last_messages(telefone)

        # Verifica o resultado
        expected_df = pd.DataFrame(mock_messages)
        pd.testing.assert_frame_equal(result, expected_df)

        # Verifica se o Redis foi chamado
        mock_redis_client.get.assert_called_once_with(f"last_messages-{telefone}-{self.bq_data.id_empresa}")

    def test_get_last_messages_cache_miss(self, mocker):
        telefone = "123456789"
        
        mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_get', return_value=None)
        mock_redis_set = mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_set')

        mock_execute = mocker.patch.object(self.bq_data, '_execute_query')
        
        mock_messages_from_db = [{"enviado_por": "user", "mensagem": "Hello"}]
        df_from_db = pd.DataFrame(mock_messages_from_db)
        
        mock_result_obj = MagicMock()
        mock_result_obj.to_dataframe.return_value = df_from_db
        mock_execute.return_value = mock_result_obj

        result_df = self.bq_data.get_last_messages(telefone)

        limit = 6
        roles_to_load_redis = ["user", "assistant"]
        
        expected_query = f"SELECT enviado_por, mensagem, model FROM {DB_SCHEMA}.conversas WHERE telefone = %s"
        expected_params = [telefone]
        
        expected_extra_query = " AND enviado_por IN (%s, %s)"
        expected_params.extend(roles_to_load_redis)
        
        expected_extra_query += " AND telefone = %s"
        expected_params.append(telefone)
        
        expected_extra_query += f" ORDER BY data_envio DESC LIMIT {limit}"
        
        mock_execute.assert_called_once_with(
            expected_query, 
            extra_query=expected_extra_query, 
            params=expected_params
        )

        expected_df_result = df_from_db.iloc[::-1]
        assert_frame_equal(result_df, expected_df_result)

        cache_key = f"last_messages-{telefone}-{self.bq_data.id_empresa}"
        expected_cache_value = json.dumps(df_from_db.to_dict(orient='records'))
        mock_redis_set.assert_called_once_with(cache_key, expected_cache_value, ex=28800)
        
    def test_save_pacto_data(self, mocker):
        mocker.patch.object(self.bq_data, '_check_if_row_exists', return_value=False)
        mock_cursor, mock_conn = MagicMock(), MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(self.bq_data, '_get_conn', return_value=mock_conn)
        mock_redis_set = mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_set')
        
        login, senha = "login_teste", "senha_teste"

        self.bq_data.save_pacto_data(login, senha)

        expected_query = f"INSERT INTO {DB_SCHEMA}.pacto_users (login, senha, id_empresa) VALUES (%s, %s, %s)"
        expected_params = (login, senha, self.bq_data.id_empresa)
        mock_cursor.execute.assert_called_once_with(
            expected_query,
            expected_params,
        )
        mock_conn.commit.assert_called_once()
        mock_redis_set.assert_called_once_with(
            f"pacto:{self.bq_data.id_empresa}", 
            json.dumps({"login": login, "senha": senha}), 
            ex=28800
        )

    @patch('src.data.bigquery_data._postgres_handler.connections.redis_client')
    def test_get_pacto_data_cache_hit(self, mock_redis_client):
        # Simula cache hit
        mock_pacto_data = {"login": "login_user", "senha": "password123"}
        mock_redis_client.get.return_value = json.dumps(mock_pacto_data)

        # Chama o método
        result = self.bq_data.get_pacto_data()

        # Verifica o resultado
        assert result == ("login_user", "password123")

        # Verifica se o Redis foi chamado
        mock_redis_client.get.assert_called_once_with(f"pacto:{self.bq_data.id_empresa}")

    def test_get_pacto_data_cache_hit(self, mocker):
        dados_em_cache = {"login": "user_cache", "senha": "password_cache"}
        mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_get', return_value=json.dumps(dados_em_cache))
        mock_execute = mocker.patch.object(self.bq_data, '_execute_query')
        user_name, user_password = self.bq_data.get_pacto_data()

        assert user_name == "user_cache"
        assert user_password == "password_cache"

        mock_execute.assert_not_called()

    def test_get_pacto_data_cache_miss(self, mocker):
        mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_get', return_value=None)
        mock_redis_set = mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_set')
        mock_execute = mocker.patch.object(self.bq_data, '_execute_query')
        
        dados_do_banco = pd.DataFrame([
            {"login": "user_db", "senha": "password_db"}
        ])
        
        mock_result_obj = MagicMock()
        mock_result_obj.to_dataframe.return_value = dados_do_banco
        mock_execute.return_value = mock_result_obj
        user_name, user_password = self.bq_data.get_pacto_data()

        assert user_name == "user_db"
        assert user_password == "password_db"

        expected_query = f"SELECT login, senha FROM {DB_SCHEMA}.pacto_users"
        mock_execute.assert_called_once_with(expected_query)

        cache_key = f"pacto:{self.bq_data.id_empresa}"
        expected_cache_value = json.dumps({"login": "user_db", "senha": "password_db"})
        mock_redis_set.assert_called_once_with(cache_key, expected_cache_value, ex=28800)

    def test_register_log(self, mocker):
        mock_cursor = MagicMock()
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(
            self.bq_data,
            '_get_conn',
            return_value=mock_conn
        )

        log_data = {
            "data": "test",
            "id_empresa": self.bq_data.id_empresa
        }
        table_name = "test_table"

        self.bq_data.register_log(log_data, table_name)

        expected_columns = list(log_data.keys())
        expected_values = list(log_data.values())
        
        placeholders = sql.SQL(", ").join(
            sql.Placeholder() * len(expected_columns)
        )
        column_names = sql.SQL(", ").join(
            map(sql.Identifier, expected_columns)
        )
        
        expected_query_obj = sql.SQL("INSERT INTO {} ({}) VALUES ({})").format(
            sql.Identifier(f"{DB_SCHEMA}", f"logs_{table_name}"),
            column_names,
            placeholders,
        )
        mock_cursor.execute.assert_called_once_with(
            expected_query_obj,
            expected_values
        )
        
        mock_conn.commit.assert_called_once()

    @patch('src.data.bigquery_data._postgres_handler.connections.redis_client')
    def test_get_last_messages_limit(self, mock_redis_client):
        telefone = "123456789"
        # Simula cache com menos mensagens do que o limite
        mock_redis_client.get.return_value = json.dumps([{"message": "Test"}])
        result = self.bq_data.get_last_messages(telefone, limit=5)
        assert len(result) == 1  # Apenas uma mensagem no cache

    @patch('src.data.bigquery_data._postgres_handler.connections.postgres_connection')
    @patch('src.data.bigquery_data._postgres_handler.connections.redis_client')
    def test_get_model_source_cache_miss(self, mock_redis_client, mock_db_connection):
        mock_redis_client.get.return_value = None
        mock_redis_client.exists.return_value = False
        mock_db_connection.query.return_value.result.return_value.to_dataframe.return_value = pd.DataFrame({"model_source": ["openai"]})
        result = self.bq_data.get_model_source()
        assert result == "openai"

    @patch('src.data.bigquery_data._postgres_handler.connections.postgres_connection')
    @patch('src.data.bigquery_data._postgres_handler.connections.redis_client')
    def test_get_model_source_cache_hit(self, mock_redis_client, mock_db_connection):
        mock_redis_client.exists.return_value = True
        mock_redis_client.get.return_value = b'custom_model'
        result = self.bq_data.get_model_source()
        assert result == "custom_model"

    def test_save_model_source(self, mocker):
        mock_redis_set = mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_set')

        mock_cursor = MagicMock()
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(self.bq_data, '_get_conn', return_value=mock_conn)

        model_source_data = "custom_model"

        self.bq_data.save_model_source(model_source_data)

        expected_query = f"UPDATE {DB_SCHEMA}.contexto_academia SET model_source = %s WHERE id_empresa = %s"
        expected_params = (model_source_data, self.bq_data.id_empresa)

        mock_cursor.execute.assert_called_once_with(expected_query, expected_params)
        mock_conn.commit.assert_called_once()

        cache_key = f"model_source:{self.bq_data.id_empresa}"
        mock_redis_set.assert_called_once_with(
            cache_key,
            model_source_data,
            ex=28800
        )

    @patch('src.data.bigquery_data._postgres_handler.connections.redis_client')
    @patch('src.data.bigquery_data._postgres_handler.connections.postgres_connection')
    def test_save_connected_phone(self, mock_db_connection, mock_redis_client):
        mock_redis_client.get.return_value = None
        telefone = "123456789"
        self.bq_data.save_connected_phone(telefone)
        # mock_redis_client.set.assert_called_once_with(f"connected_phone:{self.bq_data.id_empresa}", telefone, ex=28800)
        mock_redis_client.set.assert_has_calls([
            call(f"connected_phone:{self.bq_data.id_empresa}", telefone, ex=28800),
            call(f"empresas_telefone:{telefone}", self.bq_data.id_empresa, ex=28800)
        ])

    @pytest.mark.parametrize("exists_instance, exists_empresa", [
        (True, False),   # Cenário 1: Limpa instância antiga, faz INSERT
        (False, True),   # Cenário 2: Não limpa, faz UPDATE
        (True, True),    # Cenário 3: Limpa instância antiga, faz UPDATE
        (False, False),  # Cenário 4: Não limpa, faz INSERT
        ]
    )
    def test_save_instance_data(self, mocker, exists_instance, exists_empresa):
        mocker.patch('src.data.bigquery_data._postgres_handler.get_from_instance', return_value=("empresa-1", None))
        mocker.patch('src.data.bigquery_data._postgres_handler.get_from_empresa', return_value=("instance-old", None))
        mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_set')
        mocker.patch('src.data.bigquery_data._postgres_handler.connections.redis_client.delete')
        
        mocker.patch.object(self.bq_data, '_check_if_row_exists', side_effect=[exists_instance, exists_empresa])
        
        mock_cursor = MagicMock()
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(self.bq_data, '_get_conn', return_value=mock_conn)
        
        instance_id = "instance-1"
        token = "token-123"

        self.bq_data.save_instance_data(instance_id, token)

        expected_db_calls = []
        if exists_instance:
            cleanup_query = f"UPDATE {DB_SCHEMA}.instances SET instance_id = '', token = '' WHERE instance_id = %s"
            cleanup_params = (instance_id,)
            expected_db_calls.append(call(cleanup_query, cleanup_params))

        if exists_empresa:
            main_query = f"UPDATE {DB_SCHEMA}.instances SET instance_id = %s, token = %s, id_empresa = %s WHERE id_empresa = %s"
            main_params = (instance_id, token, self.bq_data.id_empresa, self.bq_data.id_empresa)
            expected_db_calls.append(call(main_query, main_params))
        else:
            main_query = f"INSERT INTO {DB_SCHEMA}.instances (instance_id, token, id_empresa) VALUES (%s, %s, %s)"
            main_params = (instance_id, token, self.bq_data.id_empresa)
            expected_db_calls.append(call(main_query, main_params))

        mock_cursor.execute.assert_has_calls(expected_db_calls)
        assert mock_cursor.execute.call_count == len(expected_db_calls)
        mock_conn.commit.assert_called_once()

    def test_get_empresa_cache_hit(self, mocker):
        dados_em_cache = {"nome": "Empresa Cache"}
        mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_get', return_value=json.dumps(dados_em_cache))
        mock_execute = mocker.patch.object(self.bq_data, '_execute_query')

        result = self.bq_data.get_empresa()

        assert result == dados_em_cache
        mock_execute.assert_not_called()

    def test_get_empresa_cache_miss(self, mocker):
        mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_get', return_value=None)
        mock_execute = mocker.patch.object(self.bq_data, '_execute_query')
        
        dados_do_banco_json = json.dumps({"nome": "Empresa DB"})
        mock_result_obj = MagicMock()
        mock_result_obj.to_dataframe.return_value = pd.DataFrame(
            [{"contexto_empresa_json": dados_do_banco_json}]
        )
        mock_execute.return_value = mock_result_obj

        result = self.bq_data.get_empresa()

        assert result == {"nome": "Empresa DB"}
        expected_query = f"SELECT contexto_empresa_json FROM {DB_SCHEMA}.empresas"
        mock_execute.assert_called_once_with(expected_query)

    def test_create_empresa(self, mocker):
        table_id = f"{DB_SCHEMA}.empresas"
        mock_cursor = MagicMock()
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(self.bq_data, '_get_conn', return_value=mock_conn)
        mock_redis_set = mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_set')

        data = {"nome": "Empresa Exemplo"}
        dados_json = json.dumps(data, ensure_ascii=False)

        self.bq_data.create_empresa(data)

        expected_query = f"""INSERT INTO {table_id} (contexto_empresa_json, data_ultima_atualizacao, id_empresa) VALUES (%s, %s, %s)"""
        expected_params = (dados_json, ANY, self.bq_data.id_empresa)
        
        mock_cursor.execute.assert_called_once_with(expected_query, expected_params)
        mock_conn.commit.assert_called_once()
        mock_redis_set.assert_called_once_with(f"empresas:{self.bq_data.id_empresa}", dados_json, ex=28800)

    def test_update_empresa(self, mocker):
        table_id = f"{DB_SCHEMA}.empresas"
        mock_cursor = MagicMock()
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(self.bq_data, '_get_conn', return_value=mock_conn)
        mock_redis_set = mocker.patch('src.data.bigquery_data._postgres_handler.redis_client_set')

        data = {"nome": "Empresa Atualizada"}
        dados_json = json.dumps(data, ensure_ascii=False)

        self.bq_data.update_empresa(data)

        expected_query = f"""UPDATE {table_id} SET contexto_empresa_json = %s, data_ultima_atualizacao = %s WHERE id_empresa = %s"""
        expected_params = (dados_json, ANY, self.bq_data.id_empresa)

        mock_cursor.execute.assert_called_once_with(expected_query, expected_params)
        mock_conn.commit.assert_called_once()
        mock_redis_set.assert_called_once_with(f"empresas:{self.bq_data.id_empresa}", dados_json, ex=28800)

    def test_patch_empresa(self, mocker):
        dados_atuais = {"nome": "Empresa Antiga", "setor": "Tecnologia"}
        mock_get = mocker.patch.object(self.bq_data, 'get_empresa', return_value=dados_atuais)
        mock_update = mocker.patch.object(self.bq_data, 'update_empresa')
        dados_patch = {"nome": "Empresa Nova Patch"}

        self.bq_data.patch_empresa(dados_patch)

        mock_get.assert_called_once()
        
        dados_mesclados_esperados = {"nome": "Empresa Nova Patch", "setor": "Tecnologia"}
        mock_update.assert_called_once_with(dados_mesclados_esperados)

    def test_delete_empresa(self, mocker):
        mock_cursor = MagicMock()
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mocker.patch.object(self.bq_data, '_get_conn', return_value=mock_conn)
        mock_redis_delete = mocker.patch('src.data.bigquery_data._postgres_handler.connections.redis_client.delete')

        self.bq_data.delete_empresa()

        expected_query = f"DELETE FROM {DB_SCHEMA}.empresas WHERE id_empresa = %s"
        mock_cursor.execute.assert_called_once_with(expected_query, (self.bq_data.id_empresa,))
        mock_conn.commit.assert_called_once()
        mock_redis_delete.assert_called_once_with(f"empresas:{self.bq_data.id_empresa}")

    # Tests for Department methods
    @patch('src.data.bigquery_data._postgres_handler.connections.redis_client')
    def test_create_department(self, mock_redis_client):
        table_id = f"{DB_SCHEMA}.gymbot_departamentos"
        mock_cursor = MagicMock()
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor

        with patch.object(self.bq_data, '_get_conn', return_value=mock_conn):
            mock_redis_client.scan.return_value = (0, [])

            data = {
                "id": "dept-123",
                "name": "Vendas",
                "color": "#FF0000",
                "description": "Departamento de vendas"
            }

            self.bq_data.create_department(data)

            expected_query = f"""
                INSERT INTO {table_id} (id_departamento, departamento, descricao, cor, id_empresa, data_ultima_atualizacao)
                VALUES (%s, %s, %s, %s, %s, %s)
            """
            expected_params = ("dept-123", "Vendas", "Departamento de vendas", "#FF0000", self.bq_data.id_empresa, ANY)

            mock_cursor.execute.assert_called_once_with(expected_query, expected_params)
            mock_conn.commit.assert_called_once()

    @patch('src.data.bigquery_data._postgres_handler.connections.redis_client')
    def test_update_department(self, mock_redis_client):
        table_id = f"{DB_SCHEMA}.gymbot_departamentos"
        mock_cursor = MagicMock()
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor

        with patch.object(self.bq_data, '_get_conn', return_value=mock_conn):
            mock_redis_client.scan.return_value = (0, [])

            data = {
                "id": "dept-123",
                "name": "Vendas Atualizadas",
                "color": "#00FF00",
                "description": "Departamento de vendas atualizado"
            }

            self.bq_data.update_department(data)

            expected_query = f"""
                INSERT INTO {table_id} (id_departamento, id_empresa, departamento, descricao, cor, data_ultima_atualizacao)
                VALUES (%s, %s, %s, %s, %s, %s)
                ON CONFLICT (id_departamento, id_empresa)
                DO UPDATE SET
                    departamento = EXCLUDED.departamento,
                    descricao = EXCLUDED.descricao,
                    cor = EXCLUDED.cor,
                    data_ultima_atualizacao = EXCLUDED.data_ultima_atualizacao
            """
            expected_params = ("dept-123", self.bq_data.id_empresa, "Vendas Atualizadas", "Departamento de vendas atualizado", "#00FF00", ANY)

            mock_cursor.execute.assert_called_once_with(expected_query, expected_params)
            mock_conn.commit.assert_called_once()

    def test_patch_department(self):
        current_departments = [
            {
                "id": "dept-123",
                "name": "Vendas",
                "color": "#FF0000",
                "description": "Departamento de vendas"
            }
        ]

        with patch.object(self.bq_data, 'get_departments', return_value=current_departments) as mock_get_departments, \
             patch.object(self.bq_data, 'update_department') as mock_update_department:

            data = {
                "id": "dept-123",
                "name": "Vendas Patch"
            }

            self.bq_data.patch_department(data)

            mock_get_departments.assert_called_once()
            expected_update_data = {
                "id": "dept-123",
                "name": "Vendas Patch",
                "color": "#FF0000",
                "description": "Departamento de vendas"
            }
            mock_update_department.assert_called_once_with(expected_update_data)

    @patch('src.data.bigquery_data._postgres_handler.redis_client_get')
    @patch('src.data.bigquery_data._postgres_handler.check_if_key_exists')
    def test_get_departments_cache_hit(self, mock_check_key, mock_redis_get):
        cached_data = [
            {
                "id": "dept-123",
                "name": "Vendas",
                "color": "#FF0000",
                "description": "Departamento de vendas"
            }
        ]
        mock_check_key.return_value = True
        mock_redis_get.return_value = json.dumps(cached_data)

        result = self.bq_data.get_departments()

        assert result == cached_data
        mock_check_key.assert_called_once()
        mock_redis_get.assert_called_once()

    @patch('src.data.bigquery_data._postgres_handler.redis_client_set')
    @patch('src.data.bigquery_data._postgres_handler.check_if_key_exists')
    def test_get_departments_cache_miss(self, mock_check_key, mock_redis_set):
        mock_check_key.return_value = False

        # Mock database result
        mock_result = MagicMock()
        mock_df = pd.DataFrame([{
            'id_departamento': 'dept-123',
            'departamento': 'Vendas',
            'descricao': 'Departamento de vendas',
            'cor': '#FF0000',
            'data_ultima_atualizacao': pd.Timestamp('2023-01-01')
        }])
        mock_result.to_dataframe.return_value = mock_df

        with patch.object(self.bq_data, '_execute_query', return_value=mock_result) as mock_execute_query:
            result = self.bq_data.get_departments()

            expected_result = [
                {
                    "id": "dept-123",
                    "name": "Vendas",
                    "color": "#FF0000",
                    "description": "Departamento de vendas",
                    "data_ultima_atualizacao": "2023-01-01T00:00:00"
                }
            ]

            assert result == expected_result
            mock_execute_query.assert_called_once()
            mock_redis_set.assert_called_once()

    @patch('src.data.bigquery_data._postgres_handler.connections.redis_client')
    def test_delete_department(self, mock_redis_client):
        table_id = f"{DB_SCHEMA}.gymbot_departamentos"
        mock_cursor = MagicMock()
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor

        with patch.object(self.bq_data, '_get_conn', return_value=mock_conn):
            mock_redis_client.scan.return_value = (0, [])

            data = {"id": "dept-123"}

            self.bq_data.delete_department(data)

            expected_query = f"""
                DELETE FROM {table_id} WHERE id_departamento = %s AND id_empresa = %s
            """
            expected_params = ("dept-123", self.bq_data.id_empresa)

            mock_cursor.execute.assert_called_once_with(expected_query, expected_params)
            mock_conn.commit.assert_called_once()

    @patch('src.data.bigquery_data._postgres_handler.connections.redis_client')
    def test_sync_department(self, mock_redis_client):
        current_departments = [
            {"id": "dept-1", "name": "Vendas", "description": "Desc 1"},
            {"id": "dept-2", "name": "Marketing", "description": "Desc 2"}
        ]
        input_departments = [
            {"id": "dept-1", "name": "Vendas Updated", "color": "#FF0000"},
            {"id": "dept-3", "name": "Suporte", "color": "#00FF00"}
        ]

        with patch.object(self.bq_data, 'get_departments', return_value=current_departments) as mock_get_departments, \
             patch.object(self.bq_data, 'delete_department') as mock_delete_department, \
             patch.object(self.bq_data, 'update_department') as mock_update_department:

            mock_redis_client.scan.return_value = (0, [])

            self.bq_data.sync_department(input_departments)

            # Should delete dept-2 (not in input)
            mock_delete_department.assert_called_once_with({"id": "dept-2"})

            # Should update dept-1 and dept-3
            assert mock_update_department.call_count == 2

    # Tests for Tag methods
    @patch('src.data.bigquery_data._postgres_handler.connections.redis_client')
    def test_save_tag(self, mock_redis_client):
        table_id = f"{DB_SCHEMA}.user_tags"
        mock_cursor = MagicMock()
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor

        with patch.object(self.bq_data, '_get_conn', return_value=mock_conn):
            data = {
                "phone": "123456789",
                "tag_id": 1
            }

            self.bq_data.save_tag(data)

            expected_query = f"""
                INSERT INTO {table_id} (id_empresa, telefone, id_tag, active, data_ultima_atualizacao)
                VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT (id_empresa, telefone, id_tag)
                DO UPDATE SET
                    active = EXCLUDED.active,
                    data_ultima_atualizacao = EXCLUDED.data_ultima_atualizacao
            """
            expected_params = (self.bq_data.id_empresa, "123456789", 1, True, ANY)

            mock_cursor.execute.assert_called_once_with(expected_query, expected_params)
            mock_conn.commit.assert_called_once()
            assert mock_redis_client.delete.call_count == 2  # Two cache keys deleted

    @patch('src.data.bigquery_data._postgres_handler.connections.redis_client')
    def test_remove_tag(self, mock_redis_client):
        table_id = f"{DB_SCHEMA}.user_tags"
        mock_cursor = MagicMock()
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor

        with patch.object(self.bq_data, '_get_conn', return_value=mock_conn):
            data = {
                "phone": "123456789",
                "tag_id": 1
            }

            self.bq_data.remove_tag(data)

            expected_query = f"""
                UPDATE {table_id}
                SET active = %s,
                    data_ultima_atualizacao = %s
                WHERE id_empresa = %s
                AND telefone = %s
                AND id_tag = %s
            """
            expected_params = (False, ANY, self.bq_data.id_empresa, "123456789", 1)

            mock_cursor.execute.assert_called_once_with(expected_query, expected_params)
            mock_conn.commit.assert_called_once()
            assert mock_redis_client.delete.call_count == 2  # Two cache keys deleted

    @patch('src.data.bigquery_data._postgres_handler.redis_client_get')
    @patch('src.data.bigquery_data._postgres_handler.check_if_key_exists')
    def test_get_tags_cache_hit(self, mock_check_key, mock_redis_get):
        cached_tags = [1, 2, 3]
        mock_check_key.return_value = True
        mock_redis_get.return_value = json.dumps(cached_tags)

        result = self.bq_data.get_tags("123456789")

        assert result == cached_tags
        mock_check_key.assert_called_once()
        mock_redis_get.assert_called_once()

    @patch('src.data.bigquery_data._postgres_handler.redis_client_set')
    @patch('src.data.bigquery_data._postgres_handler.check_if_key_exists')
    def test_get_tags_cache_miss(self, mock_check_key, mock_redis_set):
        table_id = f"{DB_SCHEMA}.user_tags"
        mock_check_key.return_value = False

        # Mock database result
        mock_cursor = MagicMock()
        mock_cursor.fetchall.return_value = [(1, '2023-01-01'), (2, '2023-01-02')]
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor

        with patch.object(self.bq_data, '_get_conn', return_value=mock_conn):
            result = self.bq_data.get_tags("123456789")

            expected_query = f"""
                SELECT id_tag, data_ultima_atualizacao
                FROM {table_id}
                WHERE id_empresa = %s
                AND telefone = %s
                AND active = %s
                ORDER BY data_ultima_atualizacao DESC
            """
            expected_params = (self.bq_data.id_empresa, "123456789", True)

            mock_cursor.execute.assert_called_once_with(expected_query, expected_params)
            assert result == [1, 2]
            mock_redis_set.assert_called_once()

    @patch('src.data.bigquery_data._postgres_handler.redis_client_set')
    @patch('src.data.bigquery_data._postgres_handler.check_if_key_exists')
    def test_get_tags_empty_result(self, mock_check_key, mock_redis_set):
        mock_check_key.return_value = False

        # Mock empty database result
        mock_cursor = MagicMock()
        mock_cursor.fetchall.return_value = []
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_conn

        with patch.object(self.bq_data, '_get_conn', return_value=mock_conn):
            result = self.bq_data.get_tags("123456789")

            assert result == []
            mock_redis_set.assert_called_once()

if __name__ == '__main__':
    pytest.main(args=["-vv"])
