import pytest

from src.extras.util import parse_phone

@pytest.mark.parametrize("telefone,origin,esperado", [
    # Fixos
    ("8331420224", "z_api", "+558331420224"),  # fixo sem +
    ("+558331420224", "z_api", "+558331420224"),  # fixo com +
    ("(83) 3142-0224", "z_api", "+558331420224"),  # fixo formatado

    # Celulares já com 9
    ("+5583991420224", "z_api", "+5583991420224"),
    ("83991420224", "z_api", "+5583991420224"),

    # <PERSON><PERSON><PERSON><PERSON> sem 9
    ("8361420224", "z_api", "+5583961420224"),  # adiciona 9
    ("(83) 6142-0224", "z_api", "+5583961420224"),

    # <PERSON>ros origins (não adiciona 9)
    ("8361420224", "gym_bot", "+558361420224"),
    ("(83) 6142-0224", "gym_bot", "+558361420224"),

    # <PERSON><PERSON> com 55 mas sem +
    ("558331420224", "z_api", "+558331420224"),

])
def test_parse_phone_variacoes(telefone, origin, esperado):
    assert parse_phone(telefone, origin) == esperado
