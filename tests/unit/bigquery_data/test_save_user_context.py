import pytest
import json
from unittest.mock import <PERSON><PERSON><PERSON>, patch
from src.data.bigquery_data import BigQueryData, GCP_BIGQUERY_DATASET

def test_save_user_context_insert_novo_usuario_com_id_empresa():
    """Testa se os parâmetros do job são passados corretamente quando um novo usuário
    é criado com id_empresa (deve fazer MERGE/INSERT)."""

    mock_bigquery_client = MagicMock()
    mock_redis_client_set = MagicMock()

    contexto_teste = {"dados": {"nome": "Teste"}}
    telefone_teste = "+5511999999999"
    fase_teste = "FASE_1"
    id_empresa_teste = "empresa-1"

    with patch('src.data.bigquery_data.GCP_BIGQUERY_DATASET', 'dataset_mock'), \
         patch('src.data.bigquery_data.connections.bigquery_client', mock_bigquery_client), \
         patch('src.data.bigquery_data.redis_client_set', mock_redis_client_set):

        bq = BigQueryData(id_empresa=id_empresa_teste)
        bq.save_user_context(contexto_teste, telefone_teste, fase_teste)

        # 1. Verifica se a função de query do BigQuery foi chamada
        mock_bigquery_client.query.assert_called_once()

        # 2. Obtém o objeto job_config que foi passado na chamada
        job_config_passado = mock_bigquery_client.query.call_args[1]['job_config']
        
        # 3. Extrai os parâmetros do job_config para verificação
        params = {p.name: p.value for p in job_config_passado.query_parameters}

        # 4. Asserts nos parâmetros, garantindo que os dados de entrada foram processados corretamente
        assert params["id_usuario_unico"] == f"{telefone_teste}-{id_empresa_teste}"
        assert params["telefone"] == telefone_teste
        assert params["fase_atual"] == fase_teste
        assert params["id_empresa"] == id_empresa_teste
        assert params["id_matriz"] is None
        assert '"fase_atual": "FASE_1"' in params["contexto_usuario_json"]

        # 5. Verifica se o Redis foi chamado corretamente
        redis_key_esperada = f"{telefone_teste}-{id_empresa_teste}"
        
        # 1. Verifica se a chamada ocorreu exatamente uma vez
        mock_redis_client_set.assert_called_once()
        
        # 2. Obtém os argumentos da chamada
        args, kwargs = mock_redis_client_set.call_args
        
        # 3. Faz os asserts nos argumentos de forma individual
        assert args[0] == redis_key_esperada
        assert kwargs['ex'] == 8 * 60 * 60
        
        # 4. Desserializa o contexto salvo no Redis (que é o segundo argumento)
        import json
        redis_contexto_salvo = json.loads(args[1])
        
        # 5. Verifica o conteúdo do dicionário desserializado
        assert redis_contexto_salvo['fase_atual'] == 'FASE_1'
        assert redis_contexto_salvo['dados']['nome'] == 'Teste'
        assert redis_contexto_salvo['origin_last_update'] is None

def test_save_user_context_com_id_matriz():
    """Testa se a função salva o contexto corretamente quando um 'id_matriz' é fornecido,
    criando o 'id_usuario_unico' com o valor da matriz."""
    
    mock_bigquery_client = MagicMock()
    mock_redis_client_set = MagicMock()
    
    contexto_teste = {"dados": {"nome": "Usuário Matriz"}}
    telefone_teste = "+5521888888888"
    fase_teste = "FASE_2"
    id_matriz_teste = "matriz-123"
    id_empresa_teste = "empresa-1"

    with patch('src.data.bigquery_data.GCP_BIGQUERY_DATASET', 'dataset_mock'), \
         patch('src.data.bigquery_data.connections.bigquery_client', mock_bigquery_client), \
         patch('src.data.bigquery_data.redis_client_set', mock_redis_client_set):
        
        bq = BigQueryData(id_matriz=id_matriz_teste, id_empresa=id_empresa_teste)
        bq.save_user_context(contexto_teste, telefone_teste, fase_teste)

        # Asserts no BigQuery
        mock_bigquery_client.query.assert_called_once()
        job_config_passado = mock_bigquery_client.query.call_args[1]['job_config']
        params = {p.name: p.value for p in job_config_passado.query_parameters}
        
        # Verificando os parâmetros específicos de 'id_matriz'
        id_usuario_unico_esperado = f"{telefone_teste}-{id_matriz_teste}"
        assert params["id_usuario_unico"] == id_usuario_unico_esperado
        assert params["id_empresa"] == id_empresa_teste
        assert params["id_matriz"] == id_matriz_teste
        
        # Asserts no Redis
        mock_redis_client_set.assert_called_once()
        args, kwargs = mock_redis_client_set.call_args
        assert args[0] == id_usuario_unico_esperado
        assert kwargs['ex'] == 8 * 60 * 60

def test_save_user_context_com_origin():
    """Verifica se o parâmetro 'origin' é passado corretamente para o BigQuery e o Redis."""
    
    mock_bigquery_client = MagicMock()
    mock_redis_client_set = MagicMock()
    
    contexto_teste = {"dados": {"origem": "Chatbot"}}
    telefone_teste = "+5531777777777"
    fase_teste = "FASE_3"
    id_empresa_teste = "empresa-2"
    origin_teste = "whatsapp"
    
    with patch('src.data.bigquery_data.GCP_BIGQUERY_DATASET', 'dataset_mock'), \
         patch('src.data.bigquery_data.connections.bigquery_client', mock_bigquery_client), \
         patch('src.data.bigquery_data.redis_client_set', mock_redis_client_set):
        
        bq = BigQueryData(id_empresa=id_empresa_teste)
        bq.save_user_context(contexto_teste, telefone_teste, fase_teste, origin=origin_teste)
        
        # Asserts no BigQuery
        job_config_passado = mock_bigquery_client.query.call_args[1]['job_config']
        params = {p.name: p.value for p in job_config_passado.query_parameters}
        assert params["origin_last_update"] == origin_teste

        # Asserts no Redis
        args, _ = mock_redis_client_set.call_args
        redis_contexto_salvo = json.loads(args[1])
        assert redis_contexto_salvo['origin_last_update'] == origin_teste

def test_save_user_context_contexto_vazio_ou_none():
    """Testa se a função retorna imediatamente (não chama o BigQuery ou Redis)
    quando o contexto de entrada é um dicionário vazio ou None."""
    
    mock_bigquery_client = MagicMock()
    mock_redis_client_set = MagicMock()
    
    with patch('src.data.bigquery_data.connections.bigquery_client', mock_bigquery_client), \
         patch('src.data.bigquery_data.redis_client_set', mock_redis_client_set):
        
        bq = BigQueryData(id_empresa="empresa-3")
        
        # Teste 1: Contexto é um dicionário vazio
        bq.save_user_context(context={}, telefone="+5541666666666", fase="FASE_4")
        mock_bigquery_client.query.assert_not_called()
        mock_redis_client_set.assert_not_called()
        
        # Resetando os mocks para o próximo teste
        mock_bigquery_client.reset_mock()
        mock_redis_client_set.reset_mock()
        
        # Teste 2: Contexto é None
        bq.save_user_context(context=None, telefone="+5541666666666", fase="FASE_4")
        mock_bigquery_client.query.assert_not_called()
        mock_redis_client_set.assert_not_called()

def test_save_user_context_contexto_string_json():
    """Testa se a função processa corretamente um contexto que é uma string JSON."""
    
    mock_bigquery_client = MagicMock()
    mock_redis_client_set = MagicMock()
    
    contexto_string = '{"dados": {"info": "string_test"}}'
    telefone_teste = "+5551555555555"
    fase_teste = "FASE_5"
    id_empresa_teste = "empresa-4"
    
    with patch('src.data.bigquery_data.GCP_BIGQUERY_DATASET', 'dataset_mock'), \
         patch('src.data.bigquery_data.connections.bigquery_client', mock_bigquery_client), \
         patch('src.data.bigquery_data.redis_client_set', mock_redis_client_set):
        
        bq = BigQueryData(id_empresa=id_empresa_teste)
        bq.save_user_context(contexto_string, telefone_teste, fase_teste)
        
        # Asserts no BigQuery
        job_config_passado = mock_bigquery_client.query.call_args[1]['job_config']
        params = {p.name: p.value for p in job_config_passado.query_parameters}
        
        # Verifica se a fase_atual foi adicionada ao contexto serializado
        assert '"fase_atual": "FASE_5"' in params["contexto_usuario_json"]

        # Asserts no Redis
        args, _ = mock_redis_client_set.call_args
        redis_contexto_salvo = json.loads(args[1])
        assert redis_contexto_salvo['fase_atual'] == 'FASE_5'
        assert redis_contexto_salvo['dados']['info'] == 'string_test'