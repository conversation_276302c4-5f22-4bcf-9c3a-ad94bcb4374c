import json
import sys
import importlib
import pytest
from unittest.mock import patch

# --- Redis fake com API usada pelo endpoint ---
class FakeRedis:
    def __init__(self):
        self.kv = {}
        self.lists = {}

    def get(self, key):
        val = self.kv.get(key)
        if val is None:
            return None
        return val if isinstance(val, (bytes, bytearray)) else str(val).encode("utf-8")

    def set(self, key, value, ex=None):
        self.kv[key] = str(value).encode("utf-8")

    def lpush(self, list_name, value):
        self.lists.setdefault(list_name, []).append(value)

    def last_lpush(self, list_name):
        arr = self.lists.get(list_name, [])
        return arr[-1] if arr else None


def _build_fake_taskadapter_class():
    """
    Fake do TaskAdapter que usa parse_phone REAL.
    Retorna canAnswer=False para cair no branch que enfileira 'message_sent_gymbot'.
    """
    from src.extras.util import parse_phone

    class FakeTaskAdapter:
        def __init__(self, task, origin, id_empresa):
            self._task_in = task
            self._origin = origin
            self._id_empresa = id_empresa

        @property
        def new_task(self):
            data = self._task_in.get("data", {})
            content = data.get("content", {}) if isinstance(data, dict) else {}
            phone_in = content.get("phone")
            phone_out = parse_phone(phone_in, origin="z_api")

            new_task = {
                "canAnswer": False,
                "phone": phone_out,
                "raw_phone": phone_in,
                "data": {
                    "telefone_recebido": phone_in,
                    "telefone_enviado": phone_out,
                },
            }
            return (new_task, "ok")

    return FakeTaskAdapter


@pytest.fixture
def client():
    from flask import Flask

    # Patch dos decoradores ANTES do import do módulo do blueprint
    with patch(
        "src.api.app.auth.utils.auth_wrappers.verify_origin",
        new=lambda require_auth=True, origins=None: (lambda f: f),
    ), patch(
        "src.extras.util.RoutesTracing",
        new=lambda *a, **k: (lambda f: f),
    ):
        # Garante import limpo
        sys.modules.pop("src.api.app.routes.gym_bot", None)
        gym_bot_module = importlib.import_module("src.api.app.routes.gym_bot")

        app = Flask(__name__)
        app.testing = True
        app.redis_client = FakeRedis()
        app.register_blueprint(gym_bot_module.gymbot_webhook_bp, url_prefix="/gymbot_webhook")

        with app.test_client() as c:
            yield c


def test_message_sent_numero_fixo_nao_adiciona_9(client, capsys):
    telefone_fixo = "+558331420224"  # DDI 55 + DDD 83 + 3142-0224 (fixo)

    payload = {
        "eventType": "MESSAGE_SENT",
        "content": {
            "id": "abc123",
            "phone": telefone_fixo,
            "text": "Teste envio",
        },
    }

    print(f"\n[TEST] Payload inicial: {json.dumps(payload, indent=2)}")

    FakeTaskAdapter = _build_fake_taskadapter_class()

    with patch("src.api.app.routes.gym_bot.TaskAdapter", FakeTaskAdapter):
        resp = client.post(
            "/gymbot_webhook?id_empresa=1",
            data=json.dumps(payload),
            content_type="application/json",
        )

    assert resp.status_code == 200

    redis_client = client.application.redis_client
    raw = redis_client.last_lpush("message_sent_gymbot")
    assert raw is not None, "Nada foi enfileirado em 'message_sent_gymbot'"

    enfileirada = json.loads(raw)
    print(f"[TEST] Payload final/processado na task: {json.dumps(enfileirada, indent=2)}")

    recebido = enfileirada["data"]["telefone_recebido"]
    enviado = enfileirada["data"]["telefone_enviado"]

    assert recebido == telefone_fixo
    assert enviado == telefone_fixo, "Para número fixo, não deve inserir '9'"


def test_message_sent_numero_celular_sem_9_adiciona_9_real(client, capsys):
    """
    Cenário: payload MESSAGE_SENT com celular antigo sem 9.
    Esperado: task processada com o número corrigido pelo parse_phone (9 adicionado),
    e o payload final enfileirado no Redis.
    """
    # Número com 10 dígitos após +55 (DDD 2 dígitos + número 8 dígitos)
    telefone_celular = "+551198765432"  # SP, celular antigo sem 9

    payload = {
        "eventType": "MESSAGE_SENT",
        "content": {
            "id": "cel-001",
            "phone": telefone_celular,
            "text": "Teste envio celular",
        },
    }

    print(f"[TEST] Payload inicial: {json.dumps(payload, indent=2)}")

    # Limpa a fila antes do teste
    client.application.redis_client.lists = {}

    # Usa o TaskAdapter fake que chama parse_phone real
    FakeTaskAdapter = _build_fake_taskadapter_class()

    with patch("src.api.app.routes.gym_bot.TaskAdapter", FakeTaskAdapter):
        resp = client.post(
            "/gymbot_webhook?id_empresa=1",
            data=json.dumps(payload),
            content_type="application/json",
        )

    assert resp.status_code == 200

    # Busca último item enfileirado no Redis
    raw = client.application.redis_client.last_lpush("message_sent_gymbot")
    assert raw is not None, "Nada foi enfileirado em 'message_sent_gymbot'"

    enfileirada = json.loads(raw)
    print(f"[TEST] Payload final/processado na task: {json.dumps(enfileirada, indent=2)}")

    # Verifica que parse_phone foi aplicado
    recebido = enfileirada["data"]["telefone_recebido"]
    enviado = enfileirada["data"]["telefone_enviado"]

    assert recebido == telefone_celular
    assert enviado.startswith("+55119"), "Para celular antigo, deve adicionar o 9 automaticamente"
