from flask import Flask, request, jsonify, send_file
import requests
import json

app = Flask(__name__)

BASE_API_URL = 'http://localhost:8300'

# Armazenar tokens de autenticação do Conversas
conversas_tokens = {}

def get_conversas_headers(request_headers):
    """Extrair configurações do Conversas dos headers da requisição"""
    api_url = request_headers.get('X-Conversas-Api-Url')
    api_key = request_headers.get('X-Conversas-Api-Key')

    if api_url and api_key:
        # Verificar se já temos um token válido para esta API
        token_key = f"{api_url}:{api_key}"
        if token_key in conversas_tokens:
            return {
                'api_url': api_url,
                'token': conversas_tokens[token_key]
            }
        else:
            # Tentar autenticar
            try:
                auth_response = requests.post(
                    f"{api_url}/auth/",
                    json={"api_key": api_key},
                    headers={"accept": "application/json", "Content-Type": "application/json"}
                )

                if auth_response.status_code == 200:
                    auth_data = auth_response.json()
                    token = auth_data.get('auth_token')
                    if token:
                        conversas_tokens[token_key] = token
                        return {
                            'api_url': api_url,
                            'token': token
                        }

            except Exception as e:
                print(f"Erro na autenticação do Conversas: {e}")

    return None


@app.route('/')
def index():
    """Serve the HTML frontend"""
    return send_file('departments_manager.html')


@app.route('/api/conversas/auth', methods=['POST'])
def authenticate_conversas():
    """Autenticar na API do Conversas"""
    try:
        data = request.json
        api_url = data.get('apiUrl')
        api_key = data.get('apiKey')

        if not api_url or not api_key:
            return jsonify({"error": "URL da API e chave de API são obrigatórias"}), 400

        # Remover barra final da URL se existir
        api_url = api_url.rstrip('/')

        auth_response = requests.post(
            f"{api_url}/auth/",
            json={"api_key": api_key},
            headers={"accept": "application/json", "Content-Type": "application/json"}
        )

        print(f"Conversas Auth - Status: {auth_response.status_code}")
        print(f"Response: {auth_response.text}")

        if auth_response.status_code == 200:
            auth_data = auth_response.json()
            token = auth_data.get('auth_token')
            if token:
                # Armazenar o token
                token_key = f"{api_url}:{api_key}"
                conversas_tokens[token_key] = token
                return jsonify({"message": "Autenticação realizada com sucesso", "auth_token": token}), 200
            else:
                return jsonify({"error": "Token não encontrado na resposta"}), 400
        else:
            return jsonify(auth_response.json() if auth_response.headers.get('content-type', '').startswith('application/json') else {"error": auth_response.text}), auth_response.status_code

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/departments', methods=['GET'])
def get_departments():
    """Consultar departamentos existentes"""
    try:
        empresa = request.args.get('empresa')
        integration = request.args.get('integration', 'z_api')

        if not empresa:
            return jsonify({"error": "ID da empresa é obrigatório"}), 400

        # Verificar se há configuração do Conversas
        conversas_config = get_conversas_headers(request.headers)
        headers = {}

        if conversas_config:
            headers['Authorization'] = conversas_config['token']
            # Usar a API do Conversas se configurada
            url = f"{conversas_config['api_url']}/departments?empresa={empresa}&integration={integration}"
        else:
            # Usar a API local padrão
            url = f"{BASE_API_URL}/departments?empresa={empresa}&integration={integration}"

        response = requests.get(url, headers=headers)

        print(f"GET Departments - Status: {response.status_code}")
        print(f"Response: {response.text}")

        return jsonify(response.json() if response.headers.get('content-type', '').startswith('application/json') else {"message": response.text}), response.status_code

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/departments/sync', methods=['GET'])
def sync_departments():
    """Sincronizar departamentos com WhatsApp Business"""
    try:
        empresa = request.args.get('empresa')
        integration = request.args.get('integration', 'z_api')

        if not empresa:
            return jsonify({"error": "ID da empresa é obrigatório"}), 400

        # Verificar se há configuração do Conversas
        conversas_config = get_conversas_headers(request.headers)
        headers = {}

        if conversas_config:
            headers['Authorization'] = conversas_config['token']
            url = f"{conversas_config['api_url']}/departments/sync?empresa={empresa}&integration={integration}"
        else:
            url = f"{BASE_API_URL}/departments/sync?empresa={empresa}&integration={integration}"

        response = requests.get(url, headers=headers)

        print(f"SYNC Departments - Status: {response.status_code}")
        print(f"Response: {response.text}")

        return jsonify(response.json() if response.headers.get('content-type', '').startswith('application/json') else {"message": response.text}), response.status_code

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/departments', methods=['POST'])
def create_department():
    """Criar novo departamento"""
    try:
        empresa = request.args.get('empresa')
        integration = request.args.get('integration', 'z_api')
        data = request.json

        if not empresa:
            return jsonify({"error": "ID da empresa é obrigatório"}), 400

        if not data:
            return jsonify({"error": "Dados do departamento são obrigatórios"}), 400

        # Verificar se há configuração do Conversas
        conversas_config = get_conversas_headers(request.headers)
        headers = {'Content-Type': 'application/json'}

        if conversas_config:
            headers['Authorization'] = conversas_config['token']
            url = f"{conversas_config['api_url']}/departments/?empresa={empresa}&integration={integration}"
        else:
            url = f"{BASE_API_URL}/departments/?empresa={empresa}&integration={integration}"

        response = requests.post(url, json=data, headers=headers)

        print(f"CREATE Department - Status: {response.status_code}")
        print(f"Request data: {json.dumps(data, indent=2)}")
        print(f"Response: {response.text}")

        return jsonify(response.json() if response.headers.get('content-type', '').startswith('application/json') else {"message": response.text}), response.status_code

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/departments/<department_id>', methods=['PUT'])
def update_department(department_id):
    """Editar departamento existente"""
    try:
        empresa = request.args.get('empresa')
        data = request.json

        if not empresa:
            return jsonify({"error": "ID da empresa é obrigatório"}), 400

        if not data:
            return jsonify({"error": "Dados do departamento são obrigatórios"}), 400

        # Verificar se há configuração do Conversas
        conversas_config = get_conversas_headers(request.headers)
        headers = {'Content-Type': 'application/json'}

        if conversas_config:
            headers['Authorization'] = conversas_config['token']
            url = f"{conversas_config['api_url']}/departments/{department_id}?empresa={empresa}&integration=z_api"
        else:
            url = f"{BASE_API_URL}/departments/{department_id}?empresa={empresa}&integration=z_api"

        response = requests.put(url, json=data, headers=headers)

        print(f"UPDATE Department {department_id} - Status: {response.status_code}")
        print(f"Request data: {json.dumps(data, indent=2)}")
        print(f"Response: {response.text}")

        return jsonify(response.json() if response.headers.get('content-type', '').startswith('application/json') else {"message": response.text}), response.status_code

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/departments/<department_id>', methods=['DELETE'])
def delete_department(department_id):
    """Deletar departamento"""
    try:
        empresa = request.args.get('empresa')

        if not empresa:
            return jsonify({"error": "ID da empresa é obrigatório"}), 400

        # Verificar se há configuração do Conversas
        conversas_config = get_conversas_headers(request.headers)
        headers = {}

        if conversas_config:
            headers['Authorization'] = conversas_config['token']
            url = f"{conversas_config['api_url']}/departments/{department_id}?empresa={empresa}"
        else:
            url = f"{BASE_API_URL}/departments/{department_id}?empresa={empresa}"

        response = requests.delete(url, headers=headers)

        print(f"DELETE Department {department_id} - Status: {response.status_code}")
        print(f"Response: {response.text}")

        return jsonify(response.json() if response.headers.get('content-type', '').startswith('application/json') else {"message": response.text}), response.status_code

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/tag-colors', methods=['GET'])
def get_tag_colors():
    """Obter cores disponíveis para tags"""
    try:
        empresa = request.args.get('empresa')

        if not empresa:
            return jsonify({"error": "ID da empresa é obrigatório"}), 400

        # Verificar se há configuração do Conversas
        conversas_config = get_conversas_headers(request.headers)
        headers = {}

        if conversas_config:
            headers['Authorization'] = conversas_config['token']
            url = f"{conversas_config['api_url']}/get_tag_colors?id_empresa={empresa}"
        else:
            url = f"{BASE_API_URL}/get_tag_colors?id_empresa={empresa}"

        response = requests.get(url, headers=headers)

        print(f"GET Tag Colors - Status: {response.status_code}")
        print(f"Response: {response.text}")

        return jsonify(response.json() if response.headers.get('content-type', '').startswith('application/json') else {"message": response.text}), response.status_code

    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8044, debug=True)
