<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciador de Departamentos</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        h1,
        h2 {
            color: #333;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input,
        select,
        textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        button:hover {
            background-color: #0056b3;
        }

        button.danger {
            background-color: #dc3545;
        }

        button.danger:hover {
            background-color: #c82333;
        }

        button.success {
            background-color: #28a745;
        }

        button.success:hover {
            background-color: #218838;
        }

        .departments-list {
            margin-top: 20px;
        }

        .department-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 4px;
            background-color: #f9f9f9;
        }

        .department-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .department-name {
            font-weight: bold;
            font-size: 18px;
        }

        .color-preview {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 10px;
            border: 2px solid #333;
        }

        .response-area {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 200px;
            overflow-y: auto;
        }

        .colors-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .color-option {
            display: flex;
            align-items: center;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
        }

        .color-option:hover {
            background-color: #f0f0f0;
        }

        .color-option.selected {
            background-color: #e3f2fd;
            border-color: #2196f3;
        }

        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }

        .success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>

<body>
    <h1>Gerenciador de Departamentos</h1>

    <div class="container">
        <h2>Configuração</h2>
        <div class="form-group">
            <label for="empresa">ID da Empresa:</label>
            <input type="text" id="empresa" placeholder="Ex: empresa-1" value="empresa-1">
        </div>
        <div class="form-group">
            <label for="conversasApiUrl">URL da API do Conversas (opcional):</label>
            <input type="text" id="conversasApiUrl" placeholder="Ex: https://api.conversas.ai" value="">
        </div>
        <div class="form-group">
            <label for="conversasApiKey">Chave de API do Conversas (opcional):</label>
            <input type="password" id="conversasApiKey" placeholder="Sua chave de API">
        </div>
        <button onclick="authenticateConversas()" style="background-color: #17a2b8; color: white;">Autenticar
            Conversas</button>
        <button onclick="loadTagColors()">Carregar Cores Disponíveis</button>
        <button onclick="loadDepartments()" class="success">Consultar Departamentos</button>
        <button onclick="syncDepartments()" style="background-color: #ffc107; color: #212529;">Sincronizar
            Departamentos</button>
    </div>

    <div class="container">
        <h2>Cores Disponíveis</h2>
        <div id="colorsContainer" class="colors-grid"></div>
    </div>

    <div class="container">
        <h2>Criar/Editar Departamento</h2>
        <div class="form-group">
            <label for="deptName">Nome:</label>
            <input type="text" id="deptName" placeholder="Nome do departamento">
        </div>
        <div class="form-group">
            <label for="deptDescription">Descrição (opcional):</label>
            <textarea id="deptDescription" placeholder="Descrição do departamento"></textarea>
        </div>
        <div class="form-group">
            <label>Cor Selecionada:</label>
            <div id="selectedColor">Nenhuma cor selecionada</div>
        </div>
        <button onclick="createDepartment()" class="success">Criar Departamento</button>
        <button onclick="updateDepartment()">Atualizar Departamento</button>
        <button onclick="clearForm()">Limpar Formulário</button>
    </div>

    <div class="container">
        <h2>Departamentos</h2>
        <div id="departmentsContainer" class="departments-list"></div>
    </div>

    <div class="container">
        <h2>Resposta da API</h2>
        <div id="responseArea" class="response-area">Nenhuma operação realizada ainda...</div>
    </div>

    <script>
        let selectedColorId = null;
        let selectedColorHex = null;
        let editingDepartmentId = null;
        let availableColors = [];

        function showResponse(data, isError = false) {
            const responseArea = document.getElementById('responseArea');
            responseArea.textContent = JSON.stringify(data, null, 2);
            responseArea.className = `response-area ${isError ? 'error' : 'success'}`;
        }

        function getEmpresa() {
            return document.getElementById('empresa').value.trim();
        }

        function getConversasConfig() {
            return {
                apiUrl: document.getElementById('conversasApiUrl').value.trim(),
                apiKey: document.getElementById('conversasApiKey').value.trim()
            };
        }

        async function authenticateConversas() {
            const config = getConversasConfig();

            if (!config.apiUrl || !config.apiKey) {
                showResponse({ error: 'URL da API e chave de API do Conversas são obrigatórias para autenticação' }, true);
                return;
            }

            try {
                const response = await fetch('/api/conversas/auth', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(config)
                });

                const data = await response.json();
                showResponse(data, !response.ok);

                if (response.ok) {
                    showResponse({ message: 'Autenticação no Conversas realizada com sucesso!' }, false);
                }
            } catch (error) {
                showResponse({ error: error.message }, true);
            }
        }

        async function loadTagColors() {
            const empresa = getEmpresa();
            if (!empresa) {
                showResponse({ error: 'ID da empresa é obrigatório' }, true);
                return;
            }

            const config = getConversasConfig();

            try {
                let url = `/api/tag-colors?empresa=${empresa}`;
                const headers = { 'Content-Type': 'application/json' };

                if (config.apiUrl && config.apiKey) {
                    headers['X-Conversas-Api-Url'] = config.apiUrl;
                    headers['X-Conversas-Api-Key'] = config.apiKey;
                }

                const response = await fetch(url, { headers });
                const data = await response.json();

                showResponse(data, !response.ok);

                if (response.ok) {
                    // A resposta é um objeto com id: hex
                    availableColors = data;
                    displayColors(data);
                }
            } catch (error) {
                showResponse({ error: error.message }, true);
            }
        }

        function displayColors(colorsObj) {
            const container = document.getElementById('colorsContainer');
            container.innerHTML = '';

            Object.entries(colorsObj).forEach(([id, hex]) => {
                const colorDiv = document.createElement('div');
                colorDiv.className = 'color-option';
                colorDiv.onclick = () => selectColor(id, hex, colorDiv);

                colorDiv.innerHTML = `
                    <div class="color-preview" style="background-color: ${hex}"></div>
                    <div style="margin-left: 10px;">
                        <div><strong>Cor ${id}</strong></div>
                        <div>ID: ${id}</div>
                        <div>${hex}</div>
                    </div>
                `;

                container.appendChild(colorDiv);
            });
        }

        function selectColor(colorId, colorHex, element) {
            // Remove previous selection
            document.querySelectorAll('.color-option').forEach(el => el.classList.remove('selected'));

            // Add selection to clicked element
            element.classList.add('selected');

            selectedColorId = colorId;
            selectedColorHex = colorHex;

            document.getElementById('selectedColor').innerHTML = `
                <div style="display: flex; align-items: center;">
                    <div class="color-preview" style="background-color: ${colorHex}"></div>
                    <span style="margin-left: 10px;">ID: ${colorId} - ${colorHex}</span>
                </div>
            `;
        }

        async function loadDepartments() {
            const empresa = getEmpresa();
            if (!empresa) {
                showResponse({ error: 'ID da empresa é obrigatório' }, true);
                return;
            }

            // Garantir que as cores estão carregadas antes de mostrar departamentos
            if (!availableColors || Object.keys(availableColors).length === 0) {
                await loadTagColors();
            }

            const config = getConversasConfig();

            try {
                let url = `/api/departments?empresa=${empresa}&integration=z_api`;
                const headers = { 'Content-Type': 'application/json' };

                if (config.apiUrl && config.apiKey) {
                    headers['X-Conversas-Api-Url'] = config.apiUrl;
                    headers['X-Conversas-Api-Key'] = config.apiKey;
                }

                const response = await fetch(url, { headers });
                const data = await response.json();

                showResponse(data, !response.ok);

                if (response.ok && Array.isArray(data)) {
                    displayDepartments(data);
                }
            } catch (error) {
                showResponse({ error: error.message }, true);
            }
        }

        async function syncDepartments() {
            const empresa = getEmpresa();
            if (!empresa) {
                showResponse({ error: 'ID da empresa é obrigatório' }, true);
                return;
            }

            const config = getConversasConfig();

            try {
                let url = `/api/departments/sync?empresa=${empresa}&integration=z_api`;
                const headers = { 'Content-Type': 'application/json' };

                if (config.apiUrl && config.apiKey) {
                    headers['X-Conversas-Api-Url'] = config.apiUrl;
                    headers['X-Conversas-Api-Key'] = config.apiKey;
                }

                const response = await fetch(url, { headers });
                const data = await response.json();

                showResponse(data, !response.ok);

                if (response.ok) {
                    // Recarregar departamentos após sincronização
                    setTimeout(() => loadDepartments(), 2000);
                }
            } catch (error) {
                showResponse({ error: error.message }, true);
            }
        }

        function displayDepartments(departments) {
            const container = document.getElementById('departmentsContainer');
            container.innerHTML = '';

            if (departments.length === 0) {
                container.innerHTML = '<p>Nenhum departamento encontrado.</p>';
                return;
            }

            console.log('Available colors:', availableColors);
            console.log('Departments:', departments);

            departments.forEach(dept => {
                const deptDiv = document.createElement('div');
                deptDiv.className = 'department-item';

                // Buscar a cor hex pelo ID ou usar diretamente se já for hex
                let colorHex;
                if (dept.color.startsWith('#')) {
                    // Já é um hex
                    colorHex = dept.color;
                } else {
                    // É um ID, buscar no mapeamento
                    colorHex = availableColors[dept.color] || '#cccccc';
                }
                console.log(`Department ${dept.name}: color ID ${dept.color} -> ${colorHex}`);

                // Escapar aspas na descrição para evitar problemas no onclick
                const escapedDescription = (dept.description || '').replace(/'/g, "\\'");

                deptDiv.innerHTML = `
                    <div class="department-header">
                        <div>
                            <span class="department-name">${dept.name}</span>
                            <div class="color-preview" style="background-color: ${colorHex}"></div>
                        </div>
                        <div>
                            <button onclick="editDepartment('${dept.id}', '${dept.name}', '${escapedDescription}', '${dept.color}')">Editar</button>
                            <button onclick="deleteDepartment('${dept.id}')" class="danger">Deletar</button>
                        </div>
                    </div>
                    <div><strong>ID:</strong> ${dept.id}</div>
                    <div><strong>Cor ID:</strong> ${dept.color} <span style="color: ${colorHex};">(${colorHex})</span></div>
                    ${dept.description && dept.description.trim() ? `<div><strong>Descrição:</strong> ${dept.description}</div>` : '<div><strong>Descrição:</strong> <em>Sem descrição</em></div>'}
                    ${dept.data_criacao ? `<div><strong>Criado em:</strong> ${new Date(dept.data_criacao).toLocaleString()}</div>` : ''}
                    ${dept.data_ultima_atualizacao ? `<div><strong>Última atualização:</strong> ${new Date(dept.data_ultima_atualizacao).toLocaleString()}</div>` : ''}
                `;

                container.appendChild(deptDiv);
            });
        }

        async function createDepartment() {
            const empresa = getEmpresa();
            const name = document.getElementById('deptName').value.trim();
            const description = document.getElementById('deptDescription').value.trim();

            if (!empresa || !name || selectedColorId === null) {
                showResponse({ error: 'Empresa, nome e cor são obrigatórios' }, true);
                return;
            }

            const data = {
                name: name,
                color: selectedColorId,
                description: description || undefined
            };

            const config = getConversasConfig();

            try {
                const headers = { 'Content-Type': 'application/json' };

                if (config.apiUrl && config.apiKey) {
                    headers['X-Conversas-Api-Url'] = config.apiUrl;
                    headers['X-Conversas-Api-Key'] = config.apiKey;
                }

                const response = await fetch(`/api/departments?empresa=${empresa}&integration=z_api`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                showResponse(result, !response.ok);

                if (response.ok) {
                    clearForm();
                    setTimeout(() => loadDepartments(), 1000);
                }
            } catch (error) {
                showResponse({ error: error.message }, true);
            }
        }

        function editDepartment(id, name, description, colorValue) {
            editingDepartmentId = id;
            document.getElementById('deptName').value = name;
            document.getElementById('deptDescription').value = description;

            // Determinar o ID da cor baseado no valor
            let colorId, colorHex;
            if (colorValue.startsWith('#')) {
                // É um hex, encontrar o ID correspondente
                colorHex = colorValue;
                colorId = Object.keys(availableColors).find(key => availableColors[key] === colorValue);
            } else {
                // É um ID
                colorId = colorValue;
                colorHex = availableColors[colorId];
            }

            if (colorId && colorHex) {
                const colorElement = document.querySelector(`[onclick*="selectColor('${colorId}'"]`);
                if (colorElement) {
                    selectColor(colorId, colorHex, colorElement);
                }
            }

            document.querySelector('h2').scrollIntoView();
        }

        async function updateDepartment() {
            if (!editingDepartmentId) {
                showResponse({ error: 'Nenhum departamento selecionado para edição' }, true);
                return;
            }

            const empresa = getEmpresa();
            const name = document.getElementById('deptName').value.trim();
            const description = document.getElementById('deptDescription').value.trim();

            if (!empresa || !name || selectedColorId === null) {
                showResponse({ error: 'Empresa, nome e cor são obrigatórios' }, true);
                return;
            }

            const data = {
                name: name,
                color: selectedColorId,
                description: description || undefined
            };

            const config = getConversasConfig();

            try {
                const headers = { 'Content-Type': 'application/json' };

                if (config.apiUrl && config.apiKey) {
                    headers['X-Conversas-Api-Url'] = config.apiUrl;
                    headers['X-Conversas-Api-Key'] = config.apiKey;
                }

                const response = await fetch(`/api/departments/${editingDepartmentId}?empresa=${empresa}`, {
                    method: 'PUT',
                    headers: headers,
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                showResponse(result, !response.ok);

                if (response.ok) {
                    clearForm();
                    setTimeout(() => loadDepartments(), 1000);
                }
            } catch (error) {
                showResponse({ error: error.message }, true);
            }
        }

        async function deleteDepartment(id) {
            if (!confirm('Tem certeza que deseja deletar este departamento?')) {
                return;
            }

            const empresa = getEmpresa();
            if (!empresa) {
                showResponse({ error: 'ID da empresa é obrigatório' }, true);
                return;
            }

            const config = getConversasConfig();

            try {
                const headers = { 'Content-Type': 'application/json' };

                if (config.apiUrl && config.apiKey) {
                    headers['X-Conversas-Api-Url'] = config.apiUrl;
                    headers['X-Conversas-Api-Key'] = config.apiKey;
                }

                const response = await fetch(`/api/departments/${id}?empresa=${empresa}`, {
                    method: 'DELETE',
                    headers: headers
                });

                const result = await response.json();
                showResponse(result, !response.ok);

                if (response.ok) {
                    setTimeout(() => loadDepartments(), 1000);
                }
            } catch (error) {
                showResponse({ error: error.message }, true);
            }
        }

        function clearForm() {
            document.getElementById('deptName').value = '';
            document.getElementById('deptDescription').value = '';
            document.getElementById('selectedColor').textContent = 'Nenhuma cor selecionada';
            document.querySelectorAll('.color-option').forEach(el => el.classList.remove('selected'));
            selectedColorId = null;
            selectedColorHex = null;
            editingDepartmentId = null;
        }

        // Carregar cores ao inicializar
        window.onload = function () {
            loadTagColors();
        };
    </script>
</body>

</html>