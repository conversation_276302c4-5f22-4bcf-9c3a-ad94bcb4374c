import os

db_type = os.getenv("DATABASE", "postgres").lower()

if db_type not in ["bigquery", "postgres"]:
    raise ValueError("Variável de ambiente DATABASE deve ser 'postgres' ou 'bigquery'")

if db_type == "bigquery":
    from ._bigquery_handler import BigQueryDataHandler as BigQueryData
    from ._bigquery_handler import (
        get_from_empresa,
        get_from_instance,
        get_id_empresa,
        is_rede,
        get_from_telefone,
        check_if_key_exists,
        redis_client_get,
        redis_client_set,
    )

else:
    from ._postgres_handler import PostgresDataHandler as BigQueryData
    from ._postgres_handler import (
        get_from_empresa,
        get_from_instance,
        get_id_empresa,
        is_rede,
        get_from_telefone,
        check_if_key_exists,
        redis_client_get,
        redis_client_set,
    )


__all__ = [
    "get_from_empresa",
    "get_from_instance",
    "get_id_empresa",
    "is_rede",
    "get_from_telefone",
    "check_if_key_exists",
    "redis_client_get",
    "redis_client_set",
    "BigQueryData",
]