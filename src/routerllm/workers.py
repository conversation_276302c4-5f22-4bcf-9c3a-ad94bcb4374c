from multiprocessing import Process
# from src.routerllm import messages_received_worker
from src.worker.messages_worker.messages_received_worker import run 
from src.worker.workers import Worker
from src.extras.util import monitor_health
from src.worker.health_check.start_health_check import start_health_check_server

WORKERS = [
    Worker(
        "messages_received_worker",
        run,
        6662
    ),
]

def run_workers(redis_client):
    processes = []

    for worker in WORKERS:
        process = Process(target=monitor_health(worker.run), args=(redis_client,))
        processes.append(process)
        process.start()

    health_check_process = Process(target=start_health_check_server)
    health_check_process.start()
