import logging
import json
from datetime import datetime
import uuid

from src.data.bigquery_data import BigQueryData as bq
from src.worker.messager_modules.whatsapp_messager.whatsapp_messager import WhatsappMessager
from src.worker.llm_modules.llm_selector import LLMSelector
from src.integrations.gymbot.tools.integration_tools import GymbotIntegrationTools as GBIT
from src.integrations.z_api.tools.integration_tools import ZApiIntegrationTools as ZAIT
from src.extras.config import Config
from src.extras.util import parse_phone, retry, WorkersTracer, register_indicator
from src.extras.services.transfer_service import make_transfer
from src.connections.connections import Connections

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")

connections = Connections.get_instance()


def transfer_user_to_ai_department(
    id_empresa: str, user_reference: str, channel: str = "gym_bot"
) -> None:
    """Transfer user to AI department in Gymbot"""
    if channel == "gym_bot":
        gbit = GBIT(id_empresa)
        print("[GYMBOT_TRANSFER] Obtendo departamentos para empresa:", id_empresa)
        _, departments = gbit.get_departamento()
        ai_department = None
        for department in departments:
            if department.get("name", "").lower() == "conversasai":
                ai_department = department.get("id")
                print("[GYMBOT_TRANSFER] Departamento AI encontrado:", ai_department)
                break
        if ai_department is not None:
            print("[GYMBOT_TRANSFER] Transferindo usuário para o departamento AI:", ai_department, "sessão:", user_reference)
            gbit.transfer_user_departament(ai_department, user_reference)
        else:
            print("[GYMBOT_TRANSFER] Departamento AI não encontrado para empresa:", id_empresa)
    elif channel == "z_api":
        zait = ZAIT(id_empresa)
        print("[ZAPI_TRANSFER] Transferindo usuário para o departamento AI:", user_reference)
        departments = zait.get_tags()
        ai_department = None
        for department in departments:
            if department.get("name", "").lower() == "conversasai":
                ai_department = department.get("id")
                print("[ZAPI_TRANSFER] Departamento AI encontrado:", ai_department)
                break
        if ai_department is not None:
            make_transfer(id_empresa, user_reference, ai_department)


@WorkersTracer(
    span_name_prefix=f"{__name__}.handle_send_message",
    span_description="Enviando mensagem",
    span_attributes={
        "phone": "phone",
        "first_name": "first_name",
        "last_name": "last_name",
        "id_empresa": "id_empresa",
        "data_envio": "data_envio",
        "response": "response",
        "status": "status"
    }
)
@retry(retries=3, delay=2, backoff=2, status_codes=(500,))
def handle_send_message(task, redis_client):
    id_empresa = task['id_empresa']
    data = task['data']
    logger.info(f" [*] [messages_received_worker] Enviando mensagem")
    data = json.loads(json.dumps(data))

    phone_consulta = data.get("aluno", {}).get("pessoa", {}).get("telefonesconsulta", "")
    if not phone_consulta:
        logger.info(f" [*] [messages_received_worker] Telefone não encontrado")
        return {
            "data": {
                "status": "error",
                "phone": "not_found",
                "response": "Telefone não encontrado",
                "id_empresa": id_empresa,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
            "table" : "mensagens_enviadas"
        }
    phone = str(phone_consulta).split(',', maxsplit=1)[0]
    nome = data.get("aluno", {}).get("pessoa", {}).get("nome", "")
    nome_parts = nome.split()
    first_name = nome_parts[0] if len(nome_parts) > 0 else "-"
    last_name = nome_parts[1] if len(nome_parts) > 1 else "-"

    telefone = parse_phone(phone)
    id_contexto = (
    data.get("fase_crm") or
    data.get("aluno", {}).get("fase_crm") or
    data.get("aluno", {}).get("fase_atual") or
    "LEADS_HOJE")

    situacao = data.get('aluno', {}).get('situacao', None)
    if isinstance(situacao, dict):
        situacao = situacao.get('codigo', None)

    id_conversa = redis_client.get(f"current_conversation:{telefone}-{id_empresa}")

    id_conversa = str(uuid.uuid4())
    redis_client.set(f"current_conversation:{telefone}-{id_empresa}", id_conversa)
    logger.info(f"Novo ID de conversa criado: {id_conversa} para telefone {telefone} na fase {id_contexto}")
    register_indicator(
        "conversa_iniciada",
        id_empresa,
        telefone=telefone,
        indicador="ativo",
        nome=nome
    )
    register_indicator(
        "contato_fase",
        id_empresa,
        telefone=telefone,
        indicador=id_contexto,
        nome=nome
    )

    redis_client.set(f"meta_diaria:{id_conversa}", str(True), ex=8*60*60)

    task = {
        "id_empresa": id_empresa,
        "type": "user",
        "data":
            {
                "telefone": telefone,
                "contexto": json.dumps(data),
                "fase": id_contexto,
                "origin_last_update": "messages_to_send_worker",
            }
        }

    redis_client.lpush('task_queue_bd_contexts', json.dumps(task))
    redis_client.set(f"{telefone}-{id_empresa}", json.dumps(data), ex=8*60*60)

    bq_ = bq(id_empresa=id_empresa)
    gym_context = bq_.get_gym_context()
    id_matriz = gym_context.get("chaveMatriz", None)

    if id_matriz:
        bq_ = bq(id_empresa=id_empresa, id_matriz=id_matriz)
        bq_.save_user_empresa(id_empresa, data, telefone, id_contexto)

    connected_phone = bq_.get_connected_phone()
    messager_channel = bq_.get_messager_channel()
    model_source = bq_.get_model_source()
    llm = LLMSelector(
        user_context=data,
        id_empresa=id_empresa,
        phase=id_contexto,
        telefone=telefone,
        models_source=model_source,
        origin=messager_channel
        )
    llm_response, llm_infos = llm.get_response()
    messager = WhatsappMessager(api_choice=messager_channel, bq=bq_, id_empresa=id_empresa)

    sent_message_id = messager.send_message(
        message_data=llm_response,
        phone=telefone,
        first_name=first_name,
        last_name=last_name,
        id_empresa=id_matriz or id_empresa,
        flow=Config.FLOW_NAME,
        message_type='text',
        connected_phone=connected_phone
    )

    logger.info("[GYMBOT_TRANSFER] Messager Channel: %s", messager_channel)

    user_reference = None
    if messager_channel == "gym_bot" and hasattr(
        messager.messenger, "id_session"
    ):
        if id_session := getattr(
            messager.messenger, "id_session"
        ):
            user_reference = id_session
    elif messager_channel == "z_api":
        user_reference = sent_message_id
    if user_reference:
        transfer_user_to_ai_department(id_empresa, user_reference, messager_channel)

    bq_.save_message(
        "assistant",
        llm_response,
        telefone,
        message_type="text",
        model=llm_infos.get("model", ""),
        prompt_tokens=llm_infos.get("prompt_tokens", []),
        completion_tokens=llm_infos.get("completion_tokens", []),
        n_chars=llm_infos.get("n_chars", 0),
        message_id=sent_message_id,
        provider=messager.provider,
        id_contexto=id_contexto,
        situacao=situacao
        )
    current_time = datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
    redis_client.lpush('task_queue_crm_msg_history',
                        json.dumps({"id_empresa":id_empresa,
                                    "message":f"IA: {llm_response}",
                                    "cliente": data['aluno']['codigo'],
                                    "tipoContato": "WA",
                                    "fase": id_contexto,
                                    "data": current_time}))

    return {
        "data": {
            "status": "success",
            "phone": phone,
            "response": llm_response,
            "id_empresa": id_empresa,
            "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
        },
            "table" : "mensagens_enviadas"
        }

@WorkersTracer(
    span_name_prefix=f"{__name__}.handle_disconnect_notification",
    span_description="Notificação de desconexão",
    span_attributes={
        "phone": "phone",
        "first_name": "first_name",
        "last_name": "last_name",
        "id_empresa": "id_empresa",
        "data_envio": "data_envio",
        "response": "response",
        "status": "status"
    }
)
def handle_disconnect_notification(task, redis_client):
    id_empresa = task['id_empresa']
    data = task['data']

    telefone = data['telefone_empresa']
    message = data['message']

    messager = WhatsappMessager(api_choice='z_api')

    phone = parse_phone(telefone)
    first_name = ''
    last_name = ''

    messager.send_message(
        message_data=message,
        phone=phone,
        first_name=first_name,
        last_name=last_name,
        id_empresa= "Suporte-Pacto",
        message_type='text'
    )

    return {
        "data": {
            "status": "success",
            "phone": phone,
            "response": message,
            "id_empresa": id_empresa,
            "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
        },
        "table" : "mensagens_enviadas"
    }

@WorkersTracer(
    span_name_prefix=f"{__name__}.run",
    span_description="Rodando worker",
    span_attributes={
        "phone": "phone",
        "first_name": "first_name",
        "last_name": "last_name",
        "id_empresa": "id_empresa",
    }
)
def run(redis_client, max_iter=None):

    logger.info(" [*] Waiting for tasks")
    iter_count = 0
    while True:
        if max_iter and iter_count >= max_iter:
            break

        task = redis_client.brpop(['messages_to_send', 'task_queue_disconnect_notifications', 'messages_queued'])
        
        if task:
            queue_name, task_data = task
            task = json.loads(task_data.decode('utf-8'))

            try:
                if queue_name.decode('utf-8') == 'messages_to_send':
                    response = handle_send_message(task, redis_client)
                elif queue_name.decode('utf-8') == 'task_queue_disconnect_notifications':
                    response = handle_disconnect_notification(task, redis_client)
                elif queue_name.decode('utf-8') == 'messages_queued':
                    id_empresa = task.get('id_empresa', None)
                    data = task.get('data', {})
                    phone_consulta = data.get('aluno', {}).get('pessoa', {}).get('telefonesconsulta', '')
                    phone = str(phone_consulta).split(',', maxsplit=1)[0]
                    if (
                        queue_name == 'messages_queued' 
                        and redis_client.get(f"queue_free:{id_empresa}:{phone}") is None
                    ):
                        logger.info(f" [*] [messages_received_worker] {phone} is not free yet")
                        redis_client.lpush(queue_name, json.dumps(task))
                    else:
                        redis_client.delete(f"queue_free:{id_empresa}:{phone}")
                        response = handle_send_message(task, redis_client)

                else:
                    logger.warning(f"Fila desconhecida: {queue_name.decode('utf-8')}")
                    continue

                redis_client.lpush('logs', json.dumps(response))

            except Exception as e:
                import traceback
                logger.error(f"Error: {e}")
                traceback_ = traceback.format_exc()
                logger.error(traceback_)
                data = task.get('data', {})
                try:
                    id_empresa = task['id_empresa']
                except:
                    id_empresa = None
                response = {
                    "data": {
                        "status": "error",
                        "phone": data.get('phone', data.get('telefone', None)),
                        "response": str(e),
                        "id_empresa": id_empresa,
                        "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S'),
                        "traceback": traceback_
                    },
                    "table" : "mensagens_enviadas"
                    }
                redis_client.lpush('logs', json.dumps(response))
                run(redis_client)
        iter_count += 1
