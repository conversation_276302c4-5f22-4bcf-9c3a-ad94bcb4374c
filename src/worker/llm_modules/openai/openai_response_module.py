"""Esse módulo é responsável por realizar a comunicação com a API do OpenAI"""
# Ele recebera esse dados:
# Contexto do aluno
# Contexto da academia
# Informação dos planos do aluno com link de compra
# Mensagem inicial para ser enviada ao aluno
# A ideia é esse bot funcionar como um assistente de crm para a academia

import os
import json
import logging
import time
from langchain_core.utils.function_calling import convert_to_openai_tool
from pandas import DataFrame, Timestamp
from multiprocessing import Process
import requests as req
from datetime import datetime
from openai.types.chat.chat_completion_message_tool_call import ChatCompletionMessageToolCall 
from thefuzz import fuzz

from src.extras.util import (
    register_log,
    timestamp_formatado,
    analyze_conversation_success,
    classificao_meta_diaria,
    validate_cpf,
    validar_id_empresa,
    WorkersTracer,
    register_indicator,
    retry,
    summarize_conversation,
    count_links,
    generate_whatsapp_link,
    format_seconds_to_readable
)
from src.extras.config import Config
from src.worker.entrypoint import connections
from src.data.bigquery_data import BigQueryData as bq
from src.data.data_processor import DataProcessor as dp
from src.worker.llm_modules.base_response import BaseResponse
from src.worker.llm_modules.llm_utils.models.memory_operation import MemoryOperation
from src.worker.llm_modules.llm_utils.models.periodos_aulas import PeriodosAulasEnum
from src.worker.llm_modules.llm_utils.helpers import NotificationManager
from src.extras.services.transfer_service import make_transfer
from src.integrations.pacto.tools.integration_tools import PactoIntegrationTools as PIT
from src.integrations.pacto.default_data.pacto_template_ground_rules import TEMPLATE_GROUND_RULES

import pandas as pd
from requests.exceptions import ConnectTimeout, JSONDecodeError, ReadTimeout

import unidecode
from src.integrations.gymbot.tools.integration_tools import GymbotIntegrationTools as GBIT
from src.extras.test.mock_data import MockData
from src.worker.tracing import tracer
from concurrent.futures import ThreadPoolExecutor, as_completed

logger = logging.getLogger("conversas_logger")
STOP = "STOP"
FINISH = "FINISH"
ROLES_TO_KEEP_REDIS = [role.strip() for role in os.getenv("ROLES_TO_KEEP_REDIS", "").split(",")]
BUFFER_SIZE = int(os.environ.get("BUFFER_SIZE", 10))
UNIDADE_SEARCH_SIMILARITY_THRESHOLD = int(
    os.environ.get("UNIDADE_SEARCH_SIMILARITY_THRESHOLD", 90)
)
OPENAI_MODEL = os.environ.get("OPENAI_MODEL", "gpt-4o-mini")

# TODO: Trocar todas as funções relacionadas à Pacto para o módulo de Integrações
class OpenAIResponseModule:
   """
   Classe responsável por realizar a comunicação com a API do OpenAI e permitir a execução de ações de forma autônoma.

   Args:
      user_context (dict): Contexto do usuário.
      id_empresa (str): ID da empresa.
      phase (str): ID da fase.
      telefone (str): Número de telefone do usuário.
      first_message (bool): Indica se é a primeira mensagem da conversa. Default é False.
   """
   def __init__(
        self, user_context: dict, id_empresa: str,
        phase: str, telefone: str, first_message: bool = False,
        is_group: bool = False, origin: str = "z_api", id_session: str = None,
        id_matriz: str = None, is_rede: bool = False, send_logs_to: dict | None = None,
    ) -> None:
      # Conexão com o BigQuery e OpenAI
      self.bq = bq(id_empresa=id_empresa)
      self.pit = PIT(id_empresa=id_empresa, send_logs_to=send_logs_to)
      if origin == "gym_bot":
         self.gbit = GBIT(id_empresa= id_matriz if is_rede else id_empresa)
      self.client = connections.openai_client
      # Dados específicos
      try:
         self.user_context = json.dumps(user_context)
      except:
         self.user_context = user_context

      self.gym_context = self.bq.get_gym_context()
      self.phase_id = phase
      phase = self.bq.get_phase_context(phase_name=phase)
      self.goal = phase['instrucao_ia_fase']
      self.telefone = telefone
      self.first_message = first_message
      self.id_empresa = id_empresa
      self.is_group = is_group
      self.id_matriz = id_matriz
      self.is_rede = is_rede
      self.origin = origin
      self.id_session = id_session
      self.send_logs_to = send_logs_to
      self.info_pending_forwarding = []
      self.previous_messages = []
      self.departamentos_map = {}
      self.config = {}
      self._load_config()
      self._init_system_content()
      self._init_functions()
      # salvar function_descriptions no redis
      connections.redis_client.set(
            f'function_descriptions:{self.id_empresa}',
            json.dumps(self.function_descriptions),
            ex=8*60*60
        )

      all_context = self.content.copy()
      all_context["functions"] = self.function_descriptions

      connections.redis_client.set(
        f'system_context:{self.id_empresa}:{self.telefone}',
        json.dumps(all_context),
        ex=8*60*60
      )

   def _load_config(self):
      config_data = (self.bq.get_config() or {}).get("config", {})
      self.config["disable_plans_context"] = config_data.get("desabilitarPlanos", False)
      self.config["disable_products_context"] = config_data.get("desabilitarProdutos", False)
      self.config["disable_class_context"] = config_data.get("desabilitarTurmas", False)
      self.config["disable_class"] = config_data.get("desabilitarAulaExperimental", False)
      self.config["disable_call"] = config_data.get("desabilitarLigacao", False)
      self.config["disable_visit"] = config_data.get("desabilitarVisita", False)
      self.config["disable_cpf"] = config_data.get("desabilitarCPF", False)

   @WorkersTracer(
        span_name_prefix=f"{__name__}._init_system_content",
        span_description="Iniciando o system content para o GPT model",
        span_attributes={
            "model family": "GPT",
            "personality": "personality",
        }
    )
   def _init_system_content(self):
      self.personality = self.bq.get_personality_context()

      self.estrutura = "rede" if self.is_rede else "individual"
      if self.is_rede:
         has_saved_empresa = connections.redis_client.get(f"save_empresa:{self.id_matriz}-{self.telefone}") is not None
         self.vinculo = "com-vinculo" if has_saved_empresa else "sem-vinculo"
         chain = bq(id_empresa=self.id_matriz).get_chain_context()
         self.estados = list(set([x["estado"] for x in chain if isinstance(x, dict) and "estado" in x.keys()]))
         self.cidades = list(set([x["cidade"] for x in chain if isinstance(x, dict) and "cidade" in x.keys()]))
         self.id_filiais = list(set([x["id_empresa"] for x in chain if isinstance(x, dict) and "id_empresa" in x.keys()]))

      else:
         self.vinculo = "com-vinculo"
         self.estados = [self.gym_context.get("nomeEstado", '')]
         self.cidades = [self.gym_context.get("nomeCidade", '')]
         self.id_filiais = []

      self.atuacao = "intramunicipal" if (len(self.estados) == 1 and len(self.cidades) == 1) else "intermunicipal"
      
      self.kwargs_ground_rules = {
         "gym_name": self.gym_context.get("razaoSocial", "**SEM RAZAO SOCIAL**"),
         "gym_context": dp.process_gym_data(self.gym_context),
         "city": "\n\t-" + ",\n\t-".join(self.cidades),
         "state": "\n\t-" + ",\n\t-".join(self.estados),
      }
      

      self._set_ground_rules(self.goal)

      if not self.first_message:
         self.add_previous_message(self.telefone)
   
   @WorkersTracer(
      span_name_prefix=f"{__name__}._set_ground_rules",
      span_description="Atualizando o objetivo do agente"
   )
   def _set_ground_rules(self, goal: str) -> None:
      """Atualiza o objetivo do agente."""
      memories = self.bq.get_memories(self.telefone)
      memory_text = ""
      if memories:
            memory_text = "\n".join(f'{k}: {v}' for k, v in memories.items())
      
      GR_key = self.atuacao + '_' + self.vinculo + '_' + self.estrutura
      connections.redis_client.set(
        f"ground_rules:{self.id_empresa}:{self.telefone}",
        GR_key,
        ex=8*60*60
      )
      GR_key = GR_key if GR_key in TEMPLATE_GROUND_RULES.keys()  else "intramunicipal_com-vinculo_individual"
      
      logger.info(f"[GROUND_RULES] {GR_key}")
      gym_data = TEMPLATE_GROUND_RULES.get(GR_key).format(**self.kwargs_ground_rules)
      gym_data = json.dumps(gym_data, indent=2, ensure_ascii=False).replace('\\n', '\n')

      is_client = connections.redis_client.get(f"is_client:{self.id_empresa}:{self.telefone}")
      was_register_status_called = is_client is not None
      gym_data = gym_data if 'com-vinculo' in GR_key or was_register_status_called else "Sem informações, *PRECISA* perguntar se o aluno é cliente ou não."

      self.llm_context = (
            f"Hoje é {timestamp_formatado(weekday=True)} e você sabe disso.\n"

            f"## Informações do usuário:\n"
            f"{dp.process_user_data(json.loads(self.user_context), id_empresa=self.id_empresa, telefone=self.telefone)}.\n\n"

            f"## Informações provenientes da sua memória sobre o usuário:\n"
            f"{memory_text if memory_text else 'sem memórias'}\n\n"

            f"## Informações sobre a academia:\n"
            f"{gym_data}\n"
      )
      if (
            summary := connections.redis_client.get(
                f"conversation_summary:{self.id_empresa}:{self.telefone}",
            )
            and (ts := connections.redis_client.get(
                f"conversation_summary:ts:{self.id_empresa}:{self.telefone}",
            ))
      ):
            try:
                summary = summary.decode("utf-8")
                ts = float(ts.decode("utf-8"))
                now = datetime.now().timestamp()
                elapsed = now - ts
                formatted_elapsed = format_seconds_to_readable(elapsed)
                if elapsed < 6 * 3600:
                    self.llm_context += (
                        f"## Resumo da sua última conversa com o usuário (há {formatted_elapsed}):\n"
                        f"{summary}\n"
                        "Aja como se estivesse apenas continuando a conversa, não se apresente novamente."
                    )
                else:
                    self.llm_context += (
                        f"## Resumo da sua última conversa com o usuário (ocorreu há {formatted_elapsed}):\n"
                        f"{summary}\n"
                        "Considere que faz tempo desde a última conversa; adapte sua resposta conforme o tempo passado."
                    )
            except (AttributeError, ValueError):
                pass

      self.llm_goal = (
            f"Você tem esse objetivo específico com este cliente:\n"
            f"{goal}.\n"
            f"---\n"
      )

      self.llm_instructions = (
            # Notas para lidar com situações gerais
            "\n## GERAIS\n"
            "- Nota: [Seja claro e preciso em suas respostas, sempre baseando-se nas informações fornecidas.]\n"
            "- Nota: [Você não faz cadastro de alunos em planos diretamente no sistema, apenas registra visitantes.]\n"
            "- Nota: [Se você não souber a resposta, informe que não possui a informação.]\n"
            "- Nota: [Lembre-se de não fornecer informações erradas.]\n"
            "- Nota: [Fale com os pronomes adequados para o seu gênero.]\n"
            "- Nota: [Você não sabe nada sobre a academia ou o usuário além do que é apresentado aqui.]\n"
            "- Nota: [Você não pode falar sobre modalidades ou aulas que não estão no contexto fornecido.]\n"
            "- Nota: [Você pode gerar treinos personalizados com a nossa IA *APENAS para alunos com plano ATIVO*.]\n"
            "- Nota: [Você não tem permissão para falar sobre outros tópicos que não estejam relacionados à saúde, academia e bem-estar.]\n"
            "- Nota: [Você não tem permissão para falar sobre política, religião ou qualquer outro tópico controverso.]\n"
            "- Nota: [Se for solicitado algo que você não pode fazer ou não sabe, diga que não pode.]\n"
            "- Nota: [Se perguntado sobre o horário de funcionamento da academia, não forneça informações que não estejam no contexto fornecido.]\n"
            "- Nota: [Se perguntado sobre treinar em casa, avise que é sempre recomendado buscar ajuda profissional.]\n"
            "- Nota: [Evite frases como 'Vou verificar', 'Só um momento', ou 'Deixe-me ver'. Sempre responda diretamente com a ação completa ou a resposta final. Não crie pausas artificiais nem exija interações adicionais para prosseguir.]\n"
            

            # Notas para lidar com situações muito específicas
            "\n## ESPECÍFICAS\n"
            "- Nota: [Existe a possibilidade de o usuário não ter te respondido, isso será identificável se você receber algo como:"
            " INFORMAÇÃO DO SISTEMA: ```<Instruções sobre o que fazer com a falta de resposta do usuário>```]\n"
            "- Nota: [Algumas mensagens do usuário virão acompanhadas de um contexto adicional advindo de um RAG,"
            "desde pessoas mencionadas até preferências, problemas de saúde, restrições alimentares, gostos e desgostos diversos, etc.]\n"
            "- Nota: [Quando o usuário perguntar se você consegue ouvir áudio, responda estritamente apenas 'Sim, consigo ouvir áudio.' e nada mais do isso, sem mencionar limitações.]\n"
            "- Nota: [Quando o usuário perguntar se você consegue mandar áudio, responda apenas 'Sim, consigo responder em áudio.' sem mencionar limitações.]\n"
            "- Nota: [Se necessário, use a função warn_user para alertar o usuário sobre comportamento inadequado.]\n"
            "- Nota: [Use a função save_additional_info para registrar todas as informações adicionais fornecidas pelo usuário]\n"
            "- Nota: [Se necessitar chamar uma function, então chame a function! Nesse caso, não aproveite uma chamada anterior da mesma function]\n"    
      )

      context = json.loads(self.user_context)

      situacao = context.get("aluno", {}).get("situacao", None)

      if situacao is None:
         situacao = "LEAD"
      elif isinstance(situacao, dict):
         situacao = situacao.get("codigo", "LEAD")

      if not self.config.get("disable_class", False) and situacao != "AT":
         self.llm_instructions += "Notas: [Você sempre poderá agendar aulas experimentais para alunos visitantes e leads!]\n"
      elif not self.config.get("disable_class", False) and situacao == "AT":
         self.llm_instructions += "Notas: [O aluno está ativo, portanto não pode agendar aula experimental. " + \
                                  "Recomende que ele utilize o aplicativo da academia para agendar ou ver as aulas.]\n"
      else:
         self.llm_instructions += "Notas: [Você não pode agendar aulas experimentais.]\n"

      
      if self.is_rede and was_register_status_called:
         self.llm_instructions += (
            "\n## REDE DE ACADEMIAS\n"
            "- Nota: [Você está em uma rede de academias, então você pode sugerir outras unidades da rede para o usuário.]\n"
            "- Nota: [Nunca diga que não há ou não sabe de outras unidades, antes de pesquisar com *suggest_gyms*, *suggest_gyms_by_city* ou *suggest_gyms_by_state_city*.]\n"
            "- Nota: [Quando questionado sobre quais unidades tem na rede, cite os estados e cidades com unidades disponíveis que foi informado a *VOCÊ*]\n"
            "- Nota: [Não cite nenhuma cidade/estado que não tenha sido informado a você no *SYSTEM PROMPT*]\n"
         )
      elif self.is_rede and not was_register_status_called:
         self.llm_instructions += (
            "\n## REDE DE ACADEMIAS\n"
            "- Nota: [Você está em uma rede de academias, então você pode sugerir outras unidades da rede para o usuário. Mas, antes precisa saber se ele é cliente]\n"
         )

      if self.is_group:
         self.llm_instructions += "- Nota: [Você está em um grupo, então responda apenas a mensagens direcionadas para você.]\n"

      target_id_empresa = (self.id_matriz if self.is_rede else self.id_empresa) or self.id_empresa
      self.departamentos = bq(id_empresa=target_id_empresa).get_departament_descriptions()
      logger.info(f"[LLM] origin: {self.origin}")
      logger.info(f"[LLM] Número de departamentos: {len(self.departamentos)}")
      if len(self.departamentos) > 0:
         self.llm_instructions += (
            "\n## TRANSFERÊNCIA DE DEPARTAMENTO\n"
            "- Nota: [Vocé tem a *OBRIGAÇÃO* de transferir um aluno para outro departamento quando ele solicitar por isso]\n" 
            "- Nota: [Use a função *transfer_user_departament* para tranferir o aluno]\n\n" 
            "* Os Departamentos disponíveis e as instruções para transferência são:\n"
         )
         for i, departament in enumerate(self.departamentos):
            self.departamentos_map[departament['id_departamento']] = departament['departamento']
            self.llm_instructions += (
               f"{i+1}) Departamento: {departament['departamento']}\n"
               f"id: {departament['id_departamento']}\n"
               f"Instruções: {departament['descricao']}\n\n"
            )
      
      self.ground_rules = self.llm_context + self.llm_goal + self.llm_instructions

      self.content = {
         "role": "system",
         "content": 
            f"\n# SEU CONTEXTO\n"
            f"{self.personality}\n"
            f"{self.llm_context}\n"

            f"\n# SEU OBJETIVO\n"
            f"{self.llm_goal}\n"

            f"\n# SUAS INSTRUÇÕES\n"
            f"{self.llm_instructions}\n"
      }

   # TODO: Criar um classe em um arquivo separado para gerenciar as funções, ela vai ter um openai_response_module como atributo para poder acessar os dados
   # ^ Isso vai ser difícil...
   @WorkersTracer(
        span_name_prefix=f"{__name__}._init_functions",
        span_description="Iniciando as funções para OpenAI model",
        span_attributes={
            "model family": "GPT",
        }
    )
   def _init_functions(self):
      context = json.loads(self.user_context)
      if isinstance(context, list):
         context = context[0]
      elif isinstance(context, str):
         context = json.loads(context)
      elif context is None:
         context = {}
      
      if len(self.estados) == 1 and len(self.cidades) == 1:
         self.suggest_gyms_func_name = "suggest_gyms"
      elif len(self.estados) == 1 and len(self.cidades) > 1:
         self.suggest_gyms_func_name = "suggest_gyms_by_city"
      else:
         self.suggest_gyms_func_name = "suggest_gyms_by_state_city"
      
      situacao = context.get("aluno", {}).get("situacao", None)

      if situacao is None:
         situacao = "LEAD"
      elif isinstance(situacao, dict):
         situacao = situacao.get("codigo", "LEAD")
      
      self.function_descriptions = []
      pending_cpf_for_booking = connections.redis_client.get(f"pending_cpf_for_booking:{self.id_empresa}:{self.telefone}")
      if pending_cpf_for_booking and pending_cpf_for_booking.decode('utf-8') == 'True':
         self.function_descriptions.append(
            convert_to_openai_tool(self.search_by_cpf)
         )
         self._set_ground_rules((
            "O usuário forneceu o CPF e nome para concluir um agendamento de aula. "
            "Seu único objetivo é chamar a função `search_by_cpf` com os dados fornecidos. "
            "Após a chamada, o fluxo de agendamento continuará. "
            "Não faça mais nada além de chamar a função."
         ))
         return

      if len(self.departamentos) > 0:
         self.function_descriptions.append(convert_to_openai_tool(self.transfer_user_departament))

      codigo_lead = context.get("aluno", {}).get("codigo_lead", None)
      is_client = connections.redis_client.get(f"is_client:{self.id_empresa}:{self.telefone}")

      #* Verifica se a IA já identificou se o usuário é cliente.
      if is_client is not None:
         is_client = is_client.decode('utf-8')
      elif situacao == "LEAD":
         self.function_descriptions.append(
            convert_to_openai_tool(self.register_is_client_status),
         )

         self._set_ground_rules((
            "Seu objetivo no momento é descobrir se o aluno é um cliente."
            "ANTES DE QUALQUER COISA, descobra se o aluno é um cliente."
            "Use a função register_is_client_status para  registrar o status do aluno."
            "**NÃO FALE QUE VOCÊ NÃO PODE FAZER ALGUMA COISA ENQUANTO NÃO TIVER O STATUS DO ALUNO**"
         ))
         return

      if codigo_lead is None and is_client != "False":
         self.function_descriptions.extend([
            convert_to_openai_tool(self.save_additional_info),
            convert_to_openai_tool(self.warn_user),
            convert_to_openai_tool(self.save_user_name),
            convert_to_openai_tool(self.end_conversation)
         ])
         if situacao != "AT":
             if not self.config.get("disable_class", False):
                self.function_descriptions.append(
                   convert_to_openai_tool(self.book_class)
                )
             if not self.config.get("disable_class_context", False):
                self.function_descriptions.extend([
                   convert_to_openai_tool(self.check_classes_day),
                   convert_to_openai_tool(self.check_class_details)
                ])

         if tool := self._get_additional_context_tool():
            self.function_descriptions.append(tool)

         if is_client == "True":
            if not self.config.get("disable_cpf", False):
               self.function_descriptions.append(
                  convert_to_openai_tool(self.search_by_cpf),
               )

               self._set_ground_rules((
                  "O aluno informou que é cliente, "
                  "seu objetivo no momento é solicitar o CPF "
                  "para buscar o aluno no sistema."
               ))
               return

      elif codigo_lead is None:
         self.function_descriptions.append(convert_to_openai_tool(self.save_user_name))

      self.function_descriptions.append(convert_to_openai_tool(self.register_origin))

      self.function_descriptions.extend([
         convert_to_openai_tool(self.save_additional_info),
         convert_to_openai_tool(self.warn_user),
         convert_to_openai_tool(self.end_conversation),
      ])

      saved_empresa = connections.redis_client.get(f"save_empresa:{self.id_matriz}-{self.telefone}")
      if situacao == "LEAD" and self.is_rede: 
         self.function_descriptions.extend([
            convert_to_openai_tool(self.save_user_empresa),
            convert_to_openai_tool(self.save_user_name),
            convert_to_openai_tool(getattr(self, self.suggest_gyms_func_name)),
         ])

         if not saved_empresa:    
            self._set_ground_rules((
               "Seu OBJETIVO no momento é descobrir qual unidade da rede de academia o aluno deseja conhecer.\n"
               f"- Use a função {self.suggest_gyms_func_name} para sugerir academias próximas ao aluno. \n"
               "- Depois que o aluno escolher uma unidade, use a função save_user_empresa para registrar a unidade escolhida.\n"
               "**NÃO FALE QUE VOCÊ NÃO PODE FAZER ALGUMA COISA ENQUANTO NÃO VINCULAR O ALUNO NUMA ACADEMIA!**\n"
            ))
            return

      if self.id_empresa is not None:
         self.function_descriptions.extend([
            convert_to_openai_tool(self.save_user_level),
            convert_to_openai_tool(self.save_user_birthdate),
         ])
         if not self.config.get("disable_cpf", False): # Adiciona search_by_cpf aqui apenas se não estiver desabilitado
             self.function_descriptions.append(convert_to_openai_tool(self.search_by_cpf))
         config = self.config
         disable_class = config.get("disable_class", False)
         disable_class_context = config.get("disable_class_context", False)
         disable_call = config.get("disable_call", False)
         disable_visit = config.get("disable_visit", False)
         if tool := self._get_additional_context_tool():
            self.function_descriptions.append(tool)
         if situacao != "AT":
             if not disable_class:
                self.function_descriptions.append(
                   convert_to_openai_tool(self.book_class)
                )
             if not disable_class_context:
                self.function_descriptions.extend([
                   convert_to_openai_tool(self.check_classes_day),
                   convert_to_openai_tool(self.check_class_details),
                ])
         if not disable_call:
            self.function_descriptions.append(
               convert_to_openai_tool(self.book_call)
            )
         if not disable_visit:
            self.function_descriptions.append(
                convert_to_openai_tool(self.book_visit)
            )

      if self.is_group:
         self.function_descriptions.append(convert_to_openai_tool(self.dont_respond))

      # elif situacao == "AT":
      #    self.function_descriptions.append(convert_to_openai_tool(self.generate_train))

   def _get_additional_context_prompt(self):
        config = self.config
        disable_products = config.get("disable_products_context", False)
        disable_plans = config.get("disable_plans_context", False)
        available_resources = []
        if not disable_products:
            available_resources.append("produtos")
            products_prompt = """
Além disso, se o usuário perguntar sobre os produtos, a assistente deve responder ativamente, fornecendo detalhes e 
conduzindo a venda de forma persuasiva, incentivando a conversão."""
        else:
            products_prompt = ""
        if not disable_plans:
            available_resources.append("planos")

        return f"""
Esta função é a única fonte de informações sobre o contexto específico da academia, como {" e ".join(available_resources)}
Sempre que houver necessidade de fornecer detalhes sobre os planos e produtos disponíveis,
você **deve** utilizar essa função para obter dados atualizados.

{products_prompt}

:param list context_type: Uma lista de tipos de contexto a serem recuperados do banco de dados. Deve ser uma lista de strings como {available_resources},
                      podendo ter um ou ambos. Se não tiver certeza sobre quais informações são necessárias, inclua ambas as opções.
        """
   def _get_additional_context_tool(self):
        config = self.config
        disable_products = config.get("disable_products_context", False)
        disable_plans = config.get("disable_plans_context", False)
        if disable_products and disable_plans:
            return None
        tool = convert_to_openai_tool(self.get_additional_context)
        tool["function"]["description"] = self._get_additional_context_prompt()
        return tool


   def _get_user_name(self) -> str:
      user_context = json.loads(self.user_context)
      name = user_context.get('aluno', {}).get('pessoa', {}).get('nome')
      return name

   @WorkersTracer(
        span_name_prefix=f"{__name__}.register_origin",
        span_description="Registrando a origem do usuário",
        span_attributes={
            "origin": "origin"
        }
    )
   def register_origin(self, origin: str) -> str:
      """
      Esta função é usada para registrar a origem do usuário. Ou seja, 
      se o usuário conheceu a academia por meio do instagram, facebook, site, 
      indicação, entre outros.

      :param str origin: A origem do usuário
      """
      self.bq.save_user_origin(origin, self.telefone, self.id_empresa)

      return "Origem do usuário registrada!"

   @WorkersTracer(
        span_name_prefix=f"{__name__}.save_additional_info",
        span_description="Registrando informações adicionais do usuário",
    )
   def save_additional_info(self, key: str, value: str | None, operation: MemoryOperation) -> str:
        """
        Esta função deve ser usada para registrar todo e qualquer dado adicional passado pelo usuário.
        Podem ser preferências ou objetivos pessoais, informações categóricas específicas ou qualquer outra.
        **Lembre-se! Se você já tem essa informação em alguma chave, e ela for alterada,
        você deverá usar a mesma chave!**
        **SEMPRE SEJA O MAIS DESCRITIVO POSSÍVEL NO TEXTO DA INFORMAÇÃO**
        Exemplo:
        ```
        # Aluno xpto: Eu não gosto de X.
        save_additional_info("Não oferecer X") # **RUIM**
        save_additional_info("XPTO não gosta de X, então não preciso oferecer") **BOM**
        ```

        :param str key: A categoria específica da informação.
        :param str | None value: O próprio texto da informação, pode ser nulo se operação de DELETE.
        :param MemoryOperation operation: A operação a ser feita neste bloco de memória.
        """
        stored_memory = self.bq.get_memories(self.telefone)

        ts = timestamp_formatado(weekday=True)
        match operation:
            case MemoryOperation.INSERT:
                stored_value = stored_memory.get(key, '')
                stored_memory[key] = stored_value + \
                    f"Memória inserida no dia {ts}:\n{value}"
            case MemoryOperation.UPDATE:
                stored_memory[key] = f"Memória atualizada no dia {ts}:\n{value}"
            case MemoryOperation.DELETE:
                if key not in stored_memory:
                    return "Memória atualizada."
                del stored_memory[key]
        # TODO: mandar isso pro worker
        connections.redis_client.set(
            f"memories:{self.id_empresa}:{self.telefone}",
            json.dumps(stored_memory),
            ex=8*60*60
        )

        task = {
            "type": "memory",
            "id_empresa": self.id_empresa,
            "data": {
                "memoria": stored_memory,
                "telefone": self.telefone
            }
        }
        connections.redis_client.lpush('task_queue_bd_contexts', json.dumps(task))
        return "Memória atualizada com sucesso!"

   @WorkersTracer(
        span_name_prefix=f"{__name__}.transfer_user_departament",
        span_description="Transferindo o usuário para outro departamento",
        span_attributes={
            "id_departament": "id_departament"
        }
    )
   def transfer_user_departament(self, id_departament: str) -> str:
        """
        Esta função serve para transferir o usuário para outro departamento.
        IMPORTANTE: Se o usuário pedir para falar com alguém, você deve transferi-lo para o departamento correto.
        Caso não haja um departamento adequado, você deve informar ao usuário que não é possível transferi-lo.

        :param str id_departament: O id do departamento para o qual o usuário será transferido.
        """
        if self.origin == "gym_bot":
            status, _ = self.gbit.transfer_user_departament(id_departament, id_session=self.id_session)

            if status == 200:
                register_indicator(
                    "transferencia_departamento",
                    self.id_empresa,
                    indicador=self.departamentos_map.get(id_departament),
                    telefone=self.telefone,
                    nome=self._get_user_name()
                )
                return "Usuário transferido com sucesso."
            return  "Erro ao transferir usuário."

        elif self.origin == "z_api":
            sent, message = make_transfer(self.id_empresa, self.telefone, id_departament)
            whatsapp_link = generate_whatsapp_link(self.telefone, self.origin)
            message_params = {
                "date": timestamp_formatado(),
                "user_info": self._get_user_name(),
                "department": self.departamentos_map.get(id_departament),
            }
            message = message.format(**message_params) + f"\nLink para contato com o aluno: {whatsapp_link}"
            self.info_pending_forwarding.append(message)
            if sent:
                register_indicator(
                    "transferencia_departamento_por_tag",
                    self.id_empresa,
                    indicador=self.departamentos_map.get(id_departament),
                    telefone=self.telefone,
                    nome=self._get_user_name()
                )
                return "Usuário transferido com sucesso."
            return  "Erro ao transferir usuário."



   @WorkersTracer(
        span_name_prefix=f"{__name__}.get_additional_context",
        span_description="Obtendo contexto adicional",
    )
   def get_additional_context(self, context_type: list) -> str:
      """
      Esta função é a única fonte de informações sobre o contexto específico da academia, como Planos e Produtos. 
      Sempre que houver necessidade de fornecer detalhes sobre os planos e produtos disponíveis,
      você **deve** utilizar essa função para obter dados atualizados.
      
      Além disso, se o usuário perguntar sobre os produtos, a assistente deve responder ativamente, fornecendo detalhes e 
      conduzindo a venda de forma persuasiva, incentivando a conversão.
      
      :param list context_type: Uma lista de tipos de contexto a serem recuperados do banco de dados. Deve ser uma lista de strings como ['planos', 'produtos'],
                              podendo ter um ou ambos. Se não tiver certeza sobre quais informações são necessárias, inclua ambas as opções.
      """
      additional_context = ''
      config = self.config
      disable_products = config.get("disable_products_context", False)
      disable_plans = config.get("disable_plans_context", False)
      logger.info("Obtendo contexto adicional...")
      for type_ in context_type:
         if type_ == "planos":
                if not disable_plans:
                    plans_text = dp.process_plans_data(self.bq.get_plans_context())[0]
                    additional_context += f"Planos:\n{plans_text}"
                    links_count = count_links(plans_text)
                    register_indicator(
                        identificador="links_planos",
                        id_empresa=self.id_empresa,
                        indicador=links_count,
                        telefone=self.telefone,
                        nome=self._get_user_name()
                    )
                    register_indicator(
                        identificador="informa_dados",
                        id_empresa=self.id_empresa,
                        indicador="planos",
                        telefone=self.telefone,
                        nome=self._get_user_name()
                    )
         if type_ == "produtos":
                if not disable_products:
                    products_text = dp.process_products_data(self.bq.get_products_context())
                    additional_context += f"Produtos:\n{products_text}"
                    links_count = count_links(products_text)
                    register_indicator(
                        identificador="links_produtos",
                        id_empresa=self.id_empresa,
                        indicador=links_count,
                        telefone=self.telefone,
                        nome=self._get_user_name()
                    )
                    register_indicator(
                        identificador="informa_dados",
                        id_empresa=self.id_empresa,
                        indicador="produtos",
                        telefone=self.telefone,
                        nome=self._get_user_name()
                    )
      return json.dumps(additional_context)

   @WorkersTracer(
        span_name_prefix=f"{__name__}.suggest_gyms_by_state_city_context",
        span_description="Sugerindo academias próximas ao usuário",
        span_attributes={
            "state": "state",
            "city": "city"
        }
    )
   def suggest_gyms_by_state_city(self, state: str, city: str) -> str:
      """
      Essa função deve ser usada para sugerir academias próximas ao usuário usando o estado e a cidade.

      :param str state: O nome do estado que o usuário mora. Não utilize a sigla do estado. Não pode ser um valor vazio ''
      :param str city: O cidade do usuário em que será usado para encontrar academias próximas. Não pode ser um valor vazio ''
      """
      
      lista_empresas = bq(id_empresa=self.id_matriz).get_chain_context()
      if not lista_empresas: return "Desculpe, rede ainda não cadastrada."
      if state.strip() == "": return (
         f"Peça ao usuário escolher um dos estados disponíveis: {', '.join(self.estados)}"
         f"IMPORTANTE: Você *PRECISA* chamar essa função novamente com o estado escolhido pelo usuário, "
         f"antes de chamar a função save_user_empresa."
      )
      if city.strip() == "": return (
         f"Peça ao usuário escolher uma das cidades disponíveis. {', '.join(self.cidades)}"
         f"IMPORTANTE: Você *PRECISA* chamar essa função novamente com a cidade escolhida pelo usuário, "
         f"antes de chamar a função save_user_empresa."
      )

      df_empresas = pd.json_normalize(lista_empresas)[["id_empresa", "nome_empresa", "cidade", "estado", "endereco"]]

      if (
            unidecode.unidecode(state).lower() in ["distrito federal", "df"]
            and unidecode.unidecode(city).lower() == "brasilia"
        ):
         # Cria uma série temporária sem acentos e com tudo minúsculo
         temp_states = df_empresas["estado"].apply(lambda x: unidecode.unidecode(x).lower())

         # Identifica linhas onde o estado é distrito federal ou df
         mask = temp_states.isin(['distrito federal', 'df'])

         # Para essas, setamos o estado para Distrito Federal e a cidade para Brasília
         df_empresas.loc[mask, "estado"] = 'Distrito Federal'
         df_empresas.loc[mask, "cidade"] = 'Brasília'

      df_empresas["cidade"] = df_empresas["cidade"].apply(lambda x: unidecode.unidecode(x).lower())
      df_empresas["estado"] = df_empresas["estado"].apply(lambda x: unidecode.unidecode(x).lower())

      def fuzzy_find_unidades(
        cidade_ref, estado_ref, cidade_busca, estado_busca
      ):
          return fuzz.partial_ratio(cidade_ref, cidade_busca), fuzz.partial_ratio(estado_ref, estado_busca)

      normalized_city = unidecode.unidecode(city).lower()
      normalized_state = unidecode.unidecode(state).lower()

      df_empresas[['cidade_score', 'estado_score']] = df_empresas.apply(
          lambda row: fuzzy_find_unidades(normalized_city, normalized_state, row['cidade'], row['estado']),
          axis=1,
          result_type='expand'
      )

      logger.info("[SUGGEST_GYMS] Todas as empresas:")
      for i, row in df_empresas.iterrows():
        logger.info(row)

      t = UNIDADE_SEARCH_SIMILARITY_THRESHOLD

      df_nearby = df_empresas.where(
            (df_empresas['cidade_score'] >= t) & (df_empresas['estado_score'] >= t)
      ).dropna()

      logger.info("[SUGGEST_GYMS] Empresas selecionadas:")
      for i, row in df_nearby.iterrows():
        logger.info(row)

      str_estados = "\n\t-" + ",\n\t-".join(self.estados)
      str_cidades = "\n\t-" + ",\n\t-".join(self.cidades)
      if df_nearby.empty: return "".join([
            f"Não encontramos nenhuma empresa perto da sua região.",
            f"\nOs estados com unidades disponíveis são:{str_estados}",
            f"\nAs cidades com unidades disponíveis são:{str_cidades}",
            f"\n\n*IMPORTANTE*: Você *PRECISA* chamar essa função novamente com o estado e a cidade escolhidos pelo usuário."
         ])
      
      nearby_empresas = df_nearby.to_dict(orient="records")
      #connections.redis_client.set(f"{city}_{state}_{self.id_matriz}", json.dumps(nearby_empresas, ensure_ascii=False, indent=2))
      repr_nearby_empresas = "\n\n".join([f"\tid_empresa:{empresa['id_empresa']}\t\nNome:{empresa['nome_empresa']}\n\tEndereço:{empresa['endereco']}" for empresa in nearby_empresas])
      return f"""
         Empresas em {city} - {state}:
            {repr_nearby_empresas}.
         Não mostre o id_empresa para o usuario, só nome e endereço. Depois que o usuário escolher, chame a função save_user_empresa com o id_empresa correspondente.

         *IMPORTANTE*: Sempre mostre para o usuário todas as academias encontradas, mesmo que seja muitas.
      """

   @WorkersTracer(
      span_name_prefix=f"{__name__}.suggest_gyms_by_city_context",
      span_description="Sugerindo academias próximas ao usuário",
      span_attributes={
         "city": "city"
      }
   )
   def suggest_gyms_by_city(self, city: str) -> str:
      """
      Essa função deve ser usada para sugerir academias próximas ao usuário usando a cidade.

      :param str city: O cidade do usuário em que será usado para encontrar academias próximas.
      """
      return self.suggest_gyms_by_state_city(state=self.estados[0], city=city)
   
   @WorkersTracer(
      span_name_prefix=f"{__name__}.suggest_gyms_context",
      span_description="Sugerindo academias próximas ao usuário",
      )
   def suggest_gyms(self) -> str:
      """
      Essa função deve ser usada para sugerir academias próximas ao usuário.
      """
      return self.suggest_gyms_by_state_city(state=self.estados[0], city=self.cidades[0])

   @WorkersTracer(
      span_name_prefix=f"{__name__}.register_is_client_status",
      span_description="Registrando o status do usuário",
      span_attributes={
         "status": "status"
      }
   )
   def register_is_client_status(self, status: bool) -> str:
        """
        Esta função deve ser usada quando você descobrir se o usuário é aluno ou não.
        Ela deve receber um booleano que representa se o usuário é aluno ou não.

        :param bool status: O status do usuário, True se for aluno e False se não for.
        """
        logger.info("É aluno? %s", status)
        connections.redis_client.set(
            f"is_client:{self.id_empresa}:{self.telefone}",
            str(status),
            ex=30*24*60*60  # 30 dias
        )
        if status:
            if not self.config.get("disable_cpf", False):
               return "Usuário disse que é aluno, solicitar CPF para buscar no sistema."
            else:
               return "Usuário disse que é aluno, busque pelas informações dele no sistema usando o telefone."

        message = "Usuário disse que não é aluno, pode continuar a conversa. "
        if self.is_rede:
            message += f"*IMPORTANTE*: chame a função {self.suggest_gyms_func_name} para sugerir academias próximas ao usuário."
        return message

   @WorkersTracer(
      span_name_prefix=f"{__name__}.search_by_cpf_context",
      span_description="Buscando usuário por CPF",
   )
   def search_by_cpf(self, nome: str, cpf: str) -> str:
      """
      Esta função deve ser usada se o usuário informar que já é aluno da academia.
      É importante que você busque o usuário no banco de dados da academia para obter informações sobre ele.
      Ela deve receber um CPF e o nome do usuário.

      :param str nome: O nome do usuário. "Desconhecido" não é um nome válido.
      :param str cpf: O CPF do usuário que será buscado no banco de dados.      
      """
      instrucao_para_ia = "**Diretiva de Comportamento Pós-Função:**" + \
                    "\n\n**1. O CONTEXTO MUDOU - Aja de acordo com o estado atual:**" + \
                    "\n   - A função que acabou de ser executada foi um passo intermediário. A ação principal (ex: um agendamento) **NÃO FOI CONCLUÍDA**." + \
                    "\n   - A conclusão da ação principal depende de uma futura confirmação do usuário." + \
                    "\n" + \
                    "\n**2. LÓGICA DE RESPOSTA OBRIGATÓRIA:**" + \
                    "\n   - **SE** você estava no meio de um fluxo que necessita de uma confirmação para ser finalizado (como um agendamento):" + \
                    "\n     - **NÃO** dê nenhuma confirmação sobre a ação anterior (ex: 'Ok, agendei a sua aula')." + \
                    "\n     - Continue a conversa, fazendo a próxima pergunta necessária e solicitando a confirmação do usuário para finalizar o fluxo." + \
                    "\n     - Após confirmação chame a função necessária para concluir o fluxo obrigatoriamente (exemplo: book_class para agendamento de aula)." + \
                    "\n" + \
                    "\n   - **SE** a função executada foi uma ação única e final (ex: apenas salvar uma informação pontual que não leva a outros passos):" + \
                    "\n     - Nesse caso, e somente nesse, diga que a informação específica foi salva. Ex: 'Entendido, salvei essa informação.'"
      
      logger.info("Buscando usuário por CPF...")
      logger.info(f"CPF: {cpf}")
      logger.info(f"ID da empresa: {self.id_empresa}")
      logger.info(f"ID da rede: {self.id_matriz}")

      if not validate_cpf(cpf):
         return "CPF inválido."
      if 'desconhecido' in nome.lower():
         return "Você não pode usar 'desconhecido' como nome, por favor, peça ao usuário para forneça um nome válido. Depois tente novamente."

      user_context_cpf = json.loads(self.user_context).get("aluno", {}).get("pessoa", {}).get("cpf", None)

      if user_context_cpf and user_context_cpf.replace(".", "").replace("-", "") == cpf.replace(".", "").replace("-", ""):
         connections.redis_client.delete(f"pending_cpf_for_booking:{self.id_empresa}:{self.telefone}")
         return "O usuário já foi identificado pelo CPF, não é necessário buscar novamente."

      cache = connections.redis_client.get(f"CPF_search:{self.id_empresa}:{self.telefone}")
      if cache and cache.decode('utf-8') != "Usuário não encontrado":
         return cache.decode('utf-8') + instrucao_para_ia
      
      if self.is_rede:
         logger.info("É uma rede")
         logger.info(f"ID da rede: {self.id_matriz}")
         bq_ = bq(id_empresa=self.id_matriz)
         redes_json = self.bq.get_chain_context(extra_query=f"OR id_empresa = '{self.id_matriz}'")
         lista_empresas = [rede.get("id_empresa") for rede in redes_json] if redes_json else []
         # if not lista_empresas:
         #    lista_empresas = self.pit.list_rede(self.id_matriz)
         logger.info(f"Empresas encontradas: {len(lista_empresas)}")
      else:
         logger.info("É uma empresa específica.")
         logger.info(f"ID da empresa: {self.id_empresa}")
         lista_empresas = [self.id_empresa]
      

      if len(lista_empresas) == 0:
         logger.error("Nenhuma empresa encontrada na rede.")
         return "Nenhuma empresa encontrada na rede."

      num_treads = int(os.getenv('NUM_THREADS', '4'))
      num_workers = max(min(len(lista_empresas), num_treads), 1)
      pit_list = [PIT(id_empresa_) for id_empresa_ in lista_empresas]
      logger.info("Iniciando processamento em paralelo para buscar pelo CPF.")
      with ThreadPoolExecutor(max_workers = num_workers) as executor:
         futures = {
            executor.submit(pit.get_user_by_cpf, cpf): pit
            for pit in pit_list
         }
         
         for future in as_completed(futures):
            pit = futures[future]
            id_empresa_ = pit.id_empresa
            bq_ = bq(id_empresa=id_empresa_)
            
            try:
               url, headers, params, response = future.result()
            except (ConnectTimeout, JSONDecodeError, TimeoutError, ReadTimeout) as e:
               logger.error(f"Erro na requisição: {e} na função get_user_by_cpf para a empresa {id_empresa_}.")
               continue
            
            if response.status_code == 200 and response.json().get("content", []) != []: 
               logger.info("CPF encontrado!")
               self.bq = bq_
               self.pit = pit

               user_id = response.json().get("content", [{}])[0].get("codigo", None)
               user_data = self.pit.get_user_by_id(user_id)
               self.id_empresa = id_empresa_
               logger.info("Usuário encontrado pelo id!")
               msg = dp.process_user_data(user_data, id_empresa=self.id_empresa, telefone=self.telefone)
               connections.redis_client.set(f"CPF_search:{self.id_empresa}:{self.telefone}", msg, ex=5*24*60*60)
               connections.redis_client.set(f"save_empresa:{self.id_matriz}-{self.telefone}", id_empresa_)
               self.update_user_data(user_data, "search_by_cpf_user_found")
               register_indicator(
                    identificador="aluno_identificado",
                    id_empresa=self.id_empresa,
                    indicador="cpf",
                    telefone=self.telefone,
                    nome=self._get_user_name()
               )
               connections.redis_client.delete(f"pending_cpf_for_booking:{self.id_empresa}:{self.telefone}")
               return msg + instrucao_para_ia

      logger.info("CPF não encontrado!")
      connections.redis_client.set(f"CPF_search:{self.id_empresa}:{self.telefone}", "Usuário não encontrado", ex=5*24*60*60)
      user_data = json.loads(self.user_context)
      telefone = self.telefone
      codigo_lead = self.pit.register_lead(
         nome=nome,
         telefone=telefone,
         cpf=cpf
      )

      if not codigo_lead:
         return "Usuário não encontrado e não foi registrado como lead."

      connections.redis_client.delete(f"pending_cpf_for_booking:{self.id_empresa}:{self.telefone}")
      logger.info(f"Usuário registrado como lead com sucesso: {codigo_lead}")
      id_conversa = connections.redis_client.get(
         f'current_conversation:{self.telefone}-{self.id_empresa}'
      )
      
      if id_conversa:
         id_conversa = id_conversa.decode('utf-8')
         connections.redis_client.set(
            f"meta_diaria:{id_conversa}",
            "True",
            ex=8*60*60
         )
         
      user_data["aluno"] = {
         **user_data["aluno"],
         "codigo_lead": codigo_lead,
         "pessoa": {
            **user_data.get("aluno", {}).get("pessoa", {}),
            "cpf": cpf
         }
      }
      user_data["aluno"]["pessoa"]["nome"] = nome
      self.update_user_data(user_data, "search_by_cpf_user_not_found")
      
      # CPF não encontrado 
      return instrucao_para_ia
   
   @WorkersTracer(
      span_name_prefix=f"{__name__}.save_user_name",
      span_description="Salvando nome do usuário",
      span_attributes={
         "user_name": "user_name"
      }
   )
   def save_user_name(self, user_name: str) -> str:
      """Esta função deve ser usada para salvar o nome completo do usuário no banco de dados.
      Ela deve receber uma string que representa o nome do usuário.
      
      :param str user_name: O nome completo do usuário que será salvo no banco de dados.
      """
      if 'desconhecido' in user_name.lower():
         return "Você não pode usar 'desconhecido' como nome, por favor, peça ao usuário para forneça um nome válido. Depois tente novamente."
      
      logger.info("Salvando nome do usuário...")
      contexto_aluno = json.loads(self.user_context)

      contexto_aluno["aluno"]["pessoa"]["nome"] = user_name
      contexto_aluno["aluno"]['fase_atual'] = "LEADS_HOJE"
      contexto_aluno['fase_atual'] = "LEADS_HOJE"

      self.update_user_data(contexto_aluno, "save_user_name")
      
      instrucao_para_ia = "**Diretiva de Comportamento Pós-Função:**" + \
                    "\n\n**1. O CONTEXTO MUDOU - Aja de acordo com o estado atual:**" + \
                    "\n   - A função que acabou de ser executada foi um passo intermediário. A ação principal (ex: um agendamento) **NÃO FOI CONCLUÍDA**." + \
                    "\n   - A conclusão da ação principal depende de uma futura confirmação do usuário." + \
                    "\n" + \
                    "\n**2. LÓGICA DE RESPOSTA OBRIGATÓRIA:**" + \
                    "\n   - **SE** você estava no meio de um fluxo que necessita de uma confirmação para ser finalizado (como um agendamento):" + \
                    "\n     - **NÃO** dê nenhuma confirmação sobre a ação anterior (ex: 'Ok, agendei a sua aula')." + \
                    "\n     - Continue a conversa, fazendo a próxima pergunta necessária e solicitando a confirmação do usuário para finalizar o fluxo." + \
                    "\n     - Após confirmação chame a função necessária para concluir o fluxo obrigatoriamente (exemplo: book_class para agendamento de aula)." + \
                    "\n     - *Lembre-se: não é necessário que o aluno tenha um plano ativo se o objetivo dele era agendar uma aula!*" + \
                    "\n" + \
                    "\n   - **SE** a função executada foi uma ação única e final (ex: apenas salvar uma informação pontual que não leva a outros passos):" + \
                    "\n     - Nesse caso, e somente nesse, diga que a informação específica foi salva. Ex: 'Entendido, salvei essa informação.'"
                    
      return instrucao_para_ia + \
         f"\n\nNome salvo: {user_name}"

   @WorkersTracer(
      span_name_prefix=f"{__name__}.save_user_empresa",
      span_description="Salvando empresa do usuário",
      span_attributes={
         "id_empresa": "id_empresa"
      }
   )
   def save_user_empresa(self, id_empresa_: str) -> str:
      """
      Esta função deve ser usada para registrar o interesse do usuário
      na empresa escolhida por ele.
      Ela deve receber uma string representando o id_empresa.
      IMPORTANTE: Nunca chame essa tool antes de chamar a tool suggest_gyms_by_state_city,
      pois é com resultado dela que você vai saber qual id_empresa_ deve passar como parâmetro.

      :param str id_empresa_: O id_empresa atrelado a empresa que o usuário escolheu. 
      """
      logger.info("Salvando empresa do usuário...")

      DEBUG = os.getenv("DEBUG", 'true')

      if DEBUG != 'true':
         if not validar_id_empresa(id_empresa_):
            return (
               'Esse id_empresa_ não existe, o assistente se enganou. o id empresa deve seguir o regex r"^.{25,}-[0-9]+$" '
               f'IMPORTANTE: **Pergunte** a cidade e/ou estado do usuário e chame *sem falta* a tool {self.suggest_gyms_func_name} para pegar o id_empresa_ correto.'
            )

      if id_empresa_ not in self.id_filiais:
         return f"""
         Esse id_empresa_ {id_empresa_} não existe, o assistente se enganou. os id_empresa_ possíveis são: {', '.join(self.id_filiais)}
         *NÃO MOSTRE O ID_EMPRESA PARA O USUÁRIO*
         IMPORTANTE: **Pergunte** a cidade e/ou estado do usuário e chame *sem falta* a tool {self.suggest_gyms_func_name} para pegar o id_empresa_ correto.
         """
      if self.phase_id in ["LEADS_HOJE", "LEADS"]:
         task = {
               "type": "user_empresa",
               "id_empresa": id_empresa_,
               "id_matriz": self.id_matriz,
               "data":
               {
                  "telefone": self.telefone,
                  "contexto": self.user_context,
                  "fase": "LEADS_HOJE",
                  "new_empresa": id_empresa_
               }
         }
         connections.redis_client.set(f"save_empresa:{self.id_matriz}-{self.telefone}", str(id_empresa_))
         connections.redis_client.lpush('task_queue_bd_contexts', json.dumps(task))
         self.id_empresa = id_empresa_
         self.bq = bq(id_empresa=id_empresa_)
         return "Academia selecionada com sucesso."
      return "O aluno não pode mudar de academia pelo conversa, pois já está matriculado em uma. Fale para ele entrar em contato com um atendente"

   @WorkersTracer(
      span_name_prefix=f"{__name__}.save_user_level",
      span_description="Salvando nível do usuário",
      span_attributes={
         "level_id": "level_id",
         "id_empresa": "id_empresa",
         "telefone": "telefone"
      }
   )
   def save_user_level(self, level_id: int):
      """
      Esta função serve para salvar o nível do aluno.

      :param int level_id: O código do nível do aluno, um inteiro que representa o nível.
      """
      contexto_aluno = connections.redis_client.get(f"{self.telefone}-{self.id_empresa}")
      matricula = None
      if contexto_aluno:
         contexto_aluno = json.loads(contexto_aluno)
         matricula = contexto_aluno.get("aluno", {}).get("matricula", None)

      if matricula is None:
         return "Não foi possível salvar o nível do aluno pois ele não tem cadastro no sistema, cadastre ele primeiro."

      id_empresa_ = self.id_empresa.split("-")[0]
      cod_empresa = self.id_empresa.split("-")[1]
      logger.info("Salvando nível do aluno...")
      url_micro_servicos = f"{self.pit.get_url('treinoUrl', id_empresa_)}/prest/psec/alunos/nivel/{matricula}"
      token_auth = self.pit.get_token_auth(id_empresa_)
      headers = {
         "Authorization": f"Bearer {token_auth}",
         "accept": "*/*",
         "Content-Type": "application/json",
         "empresaId": cod_empresa
      }
      body = {"nivelId":level_id}
      response = req.put(url_micro_servicos, headers=headers, json=body)
      register_log(url_micro_servicos, body, headers, "PUT", response, "save_user_level", self.id_empresa, None, send_logs_to=self.send_logs_to)
      # try:
      #    return response.json()
      # except:
      #    return response.text
      # finally:
      #    return f"{response} {response.status_code}"
      
      if (response.status_code == 200):
         detalhes_aluno = json.loads(self.user_context)
         detalhes_aluno["aluno"]["nivel"] = level_id
         self.update_user_data(detalhes_aluno, "save_user_level")
         
         return "Nível do aluno salvo com sucesso. " + \
               "Agora, pergunte ao usuário se pode prosseguir com o agendamento da aula que ele havia solicitado anteriormente."
      else:
         return "Houve um erro ao salvar o nível."
  
         
   @WorkersTracer(
      span_name_prefix=f"{__name__}.save_user_birthdate",
      span_description="Salvando data de nascimento do aluno",
      span_attributes={
         "data_nascimento": "data_nascimento"
      }
   )
   def save_user_birthdate(self, data_nascimento: str) -> str:
      """
      Esta função serve para salvar a data de nascimento do aluno.

      :param str data_nascimento: A data de nascimento do aluno, neste formato: yyyy-mm-dd.
      """
      id_empresa_ = self.id_empresa.split("-")[0]
      cod_empresa = self.id_empresa.split("-")[1]
      logger.info("Salvando data de nascimento do aluno...")
      url = f"{self.pit.get_url('admMsUrl', id_empresa_)}/v1/cliente"
      token_auth = self.pit.get_token_auth(id_empresa_)      
      detalhes_aluno = json.loads(self.user_context)
      codigo = detalhes_aluno.get("aluno", {}).get("codigo", None)
      req_body = {
         "nome": detalhes_aluno.get("aluno", {}).get("pessoa", {}).get("nome", None),
         "empresa": cod_empresa,
         "celular": str(self.telefone).replace("+55", ""),
         "dataNascimento": data_nascimento,
      }
      if codigo is not None:
          req_body["codigo"] = codigo
          headers = {
             "Authorization": f"Bearer {token_auth}",
             "accept": "*/*",
             "Content-Type": "application/json"
          }
          response = req.post(url, json=req_body, headers=headers, timeout=10)
          register_log(url, req_body, headers, "POST", response, "save_user_birthdate", self.id_empresa, None, send_logs_to=self.send_logs_to)
          if response.status_code == 200:
             # Tirar do formato yyyy-mm-dd e colocar no formato dd/mm/yyyy HH:MM:SS
             try:
                 data_nascimento = datetime.strptime(data_nascimento, "%Y-%m-%d").strftime("%d/%m/%Y %H:%M:%S")
             except ValueError as e:
                 logger.error(" [SAVE_USER_BIRTHDATE] Erro ao converter data: {e}")
             detalhes_aluno["aluno"]["pessoa"]["dataNasc"] = data_nascimento
             if codigo is None:
                   detalhes_aluno["aluno"]["codigo"] = response.json().get("content", {}).get("codigo", None)
                   detalhes_aluno["aluno"]["matricula"] = response.json().get("content", {}).get("matricula", None)
                   detalhes_aluno["aluno"]["situacao"] = "VI"
                
             self.update_user_data(detalhes_aluno, "save_user_birthdate")
             return ("Data de nascimento salva com sucesso! "
                     "Agora, pergunte ao usuário se pode prosseguir com o agendamento da aula que ele havia solicitado anteriormente.")
          elif response.status_code == 500:
             #! Quando o endpoint retorna 500, ele atualiza a data de nascimento do usuário.
             #! Isto é uma solução paliativa pela demanda e o endpoint deve ser corrigido futuramente.
             # TODO: Resolver a solução paliativa.
             
             # Tirar do formato yyyy-mm-dd e colocar no formato dd/mm/yyyy HH:MM:SS
             try:
                 data_nascimento = datetime.strptime(data_nascimento, "%Y-%m-%d").strftime("%d/%m/%Y %H:%M:%S")
             except ValueError as e:
                 logger.error(" [SAVE_USER_BIRTHDATE] Erro ao converter data: {e}")
             detalhes_aluno["aluno"]["pessoa"]["dataNasc"] = data_nascimento
             if codigo is None:
                   detalhes_aluno["aluno"]["codigo"] = response.json().get("content", {}).get("codigo", None)
                   detalhes_aluno["aluno"]["matricula"] = response.json().get("content", {}).get("matricula", None)
                   detalhes_aluno["aluno"]["situacao"] = "VI"
             
             self.update_user_data(detalhes_aluno, "save_user_birthdate")
             
             return ("Data de nascimento salva com sucesso! "
                     "Agora, pergunte ao usuário se pode prosseguir com o agendamento da aula que ele havia solicitado anteriormente.")
      else:
            detalhes_aluno["aluno"]["pessoa"]["dataNasc"] = data_nascimento
            self.update_user_data(detalhes_aluno, "save_user_birthdate")
            return "Data de nascimento salva com sucesso! " + \
                  "Agora, pergunte ao usuário se pode prosseguir com o agendamento da aula que ele havia solicitado anteriormente."

      return "Erro ao salvar data de nascimento."

   def end_conversation(self) -> None:
      """
      Esta função deve ser executada se o usuário se despedir,
      ou se você identificar que sua missão foi cumprida.

      Se perceber que o usuário teve sua solicitação resolvida, 
      agradeça ao contato e finalize a conversa. Aqui vão algumas
      formas de identificar se o usuário está se despedindo:
         - mensagens como "só isso mesmo, obrigado"/"só isso mesmo, obrigado"
         - o usuário fala obrigado várias vezes, repetidamente
         - o usuario pode interagir/reagir sem que necessariamente ele precise de algo

      Obs: sempre verifique se o usuário teve sua solicitação devidamente atendida, 
      caso isso tenha sido validado corretamente e o usuário foi bem atendido,
      só se despeça uma única vez, a não ser que o usuário faça alguma pergunta.
      """
      with tracer.start_as_current_span("end_conversation-analyze_succes") as span:
         span.set_attribute("telefone", self.telefone)
         span.set_attribute("id_empresa", self.id_empresa)

         logger.info("Finalizando conversa...")
         id_conversa = connections.redis_client.get(
            f'current_conversation:{self.telefone}-{self.id_empresa}'
         )
         if id_conversa:
            id_conversa = id_conversa.decode('utf-8')
         else:
            id_conversa = None

         success, description, prompt = analyze_conversation_success(
            self.telefone,
            self.goal,
            self.bq,
            id_conversa=id_conversa
         )

         summary = summarize_conversation(self.telefone, self.bq, id_conversa=id_conversa)
   
         connections.redis_client.set(
            f"conversation_summary:{self.id_empresa}:{self.telefone}",
            summary
         )
         connections.redis_client.set(
            f"conversation_summary:ts:{self.id_empresa}:{self.telefone}",
            datetime.now().timestamp()
         )


         message_placeholder = [
             {
                 "enviado_por": "assistant",
                 "mensagem": "...",
             }
         ]
         connections.redis_client.set(
             f"last_messages-{self.telefone}-{self.id_empresa}",
             json.dumps(message_placeholder),
             ex=24*60*60
         )

         span.set_attribute("success", success)
         span.set_attribute("description", description)

         process = Process(
               target=self.bq.save_conversation_analysis,
               args=(
                  self.telefone,
                  prompt,
                  description,
                  success
               )
            )
         process.start()
         span.set_attribute("analysis_saved", True)
         logger.info("Análise de sucesso da conversa finalizada.")
         register_indicator(
               "conversa_finalizada",
               self.id_empresa,
               telefone=self.telefone,
               nome=self._get_user_name(),
               meta={"success": success}
         )

      with tracer.start_as_current_span("end_conversation-meta_diaria") as span:
         logger.info("Resolvendo a meta diária")
         span.set_attribute("telefone", self.telefone)
         span.set_attribute("id_empresa", self.id_empresa)
         span.set_attribute("id_conversa", id_conversa)

         meta_diaria = connections.redis_client.get(f"meta_diaria:{id_conversa}")
         if meta_diaria and meta_diaria.decode('utf-8') == "True":
            span.set_attribute("meta_diaria", True)
            classification, action_description = classificao_meta_diaria(
               self.telefone,
               self.bq,
               self.pit,
               id_conversa
            )
            logger.info("Classificação da meta diária: %s", classification)
            logger.info("Descrição da ação: %s", action_description)
            if not classification:
               return FINISH
            span.set_attribute("classification", classification)
            span.set_attribute("action_description", action_description)
            process = Process(
               target=self.bq.save_conversation_analysis,
               args=(
                  self.telefone,
                  prompt,
                  action_description,
                  classification
               )
            )
            process.start()

            user_data = json.loads(self.user_context)
            codigo_cliente = user_data.get("aluno", {}).get("codigo", None)
            codigo_lead = user_data.get("aluno", {}).get("codigo_lead", None)

            register_indicator(
               identificador="conclusao_meta_diaria",
               id_empresa=self.id_empresa,
               telefone=self.telefone,
               indicador=classification,
               nome=self._get_user_name(),
               meta={"description": (action_description or "")}
            )

            self.pit.save_meta_diaria(
                  id_conversa=id_conversa,
                  classificacao=classification,
                  fase_atual=self.phase_id,
                  descricao_acao=action_description,
                  codigo_cliente=codigo_cliente,
                  codigo_lead=codigo_lead
            )

      # Transferir a pessoa de volta para o departamento padrão
      if self.origin == "gym_bot":
            _, departments = self.gbit.get_departamento()
            default_department = None
            for department in departments:
                if not isinstance(department, dict):
                    continue
                if department.get("isDefault") is True:
                    default_department = department.get("id")
                    break
            if default_department is not None:
                self.gbit.transfer_user_departament(default_department, self.id_session)

      return FINISH

   @WorkersTracer(
      span_name_prefix=f"{__name__}.dont_respond",
      span_description="Não respondendo"
   )
   def dont_respond(self) -> str:
      """"Esta função deve ser chamada caso a mensagem não seja direcionada a você, ou caso o usuário esteja claramente se referindo a outra pessoa."""
      logger.info("Não respondendo...")
      return STOP

   @WorkersTracer(
      span_name_prefix=f"{__name__}.warn_user",
      span_description="Aviso ao usuário"
   )
   def warn_user(self) -> str:
      """
      Esta função serve para dar um aviso ao usuário, em qualquer sinal de que ele esteja falando coisas fora do contexto da conversa, ou mandando mensagens repetitivas.
      É importante que você dê um aviso ao usuário por meio desta função, para que ele saiba que está fazendo algo errado.
      NÃO CHAME ESSA FUNÇÃO QUANDO O USUÁRIO QUIZER OU PEDIR PARA SER TRANFERIDO PARA FALAR COM ALGUÉM!
      """
      user_strikes = connections.redis_client.get(f"strikes:{self.telefone}-{self.id_empresa}")
      if user_strikes:
         user_strikes = json.loads(user_strikes).get("user_strikes", 0)
         user_strikes += 1
      else:
         user_strikes = 1
      connections.redis_client.set(f"strikes:{self.telefone}-{self.id_empresa}", json.dumps({"user_strikes": user_strikes}), ex=24*60*60)
      if user_strikes >= 3:
         return f"Usuário marcado com {user_strikes} strikes, estará bloqueado por 24 horas, mande o contato da academia para que ele solicite desbloqueio."
      
      return f"Usuário marcado com {user_strikes} strikes. Apenas 3 strikes são permitidos."
      
   @WorkersTracer(
      span_name_prefix=f"{__name__}.check_classes_day",
      span_description="Verificando aulas disponíveis",
      span_attributes={
         "dia": "dia",
         "mes": "mes",
         "periodo": "periodo",
         "id_empresa": "id_empresa",
      }
   )
   def check_classes_day(self, dia: int, mes: int, periodo: PeriodosAulasEnum) -> str:
      """
      Você **SEMPRE** deve usar a ferramenta `check_classes_day` como o primeiro passo antes de responder a qualquer pergunta sobre horários de aulas ou antes de agendar uma aula experimental.

      ## Fluxo de Trabalho Obrigatório
      1.  **Identifique a Intenção:** Se o usuário perguntar "quais aulas tem?", "tem aula amanhã?", "posso marcar uma aula?", ative este fluxo.
      2.  **Verifique os Parâmetros:** Para usar `check_classes_day`, você precisa de `dia`, `mes` e `periodo`.
      3.  **Colete as Informações:**
         * Se o usuário não fornecer o dia e o mês, pergunte a ele. Ex: "Claro! Para qual dia e mês você gostaria de verificar a disponibilidade?".
         * Traduza termos relativos como "hoje" (dia 2 de julho), "amanhã" (dia 3 de julho) para `dia` e `mes` numéricos.
      4.  **Execute a Ferramenta:** Chame `check_classes_day` com os parâmetros coletados.
      5.  **Responda ao Usuário:** Use o resultado da função para informar as aulas disponíveis ou para confirmar o agendamento. Se não houver aulas, informe o usuário.

      :param int dia: O dia do mês que a aula ocorre.
      :param int mes: O mês que a aula ocorre.
      :param PeriodosAulasEnum perido: O período da aula
      """
      #* Instruções extras para a resposta da IA.
      instrucoes_para_ia = "\n\n**Instruções para sua resposta:**" + \
                  " Liste todas as aulas futuras se o usuário não informou um horário em específico." + \
                  "\n- **NUNCA revele IDs:** Não mostre o código, ID da turma ou qualquer outro identificador numérico das aulas." + \
                  "\n- **Use a linguagem correta:** Ao falar de uma aula disponível, diga 'A aula é às...' ou 'Temos às...'. A palavra 'agendada' é proibida neste momento." + \
                  "\n- **Quando usar 'agendado(a)':** Use a palavra 'agendado(a)' somente na mensagem de confirmação, após a função `book_class` ser chamada e executada com sucesso."


      #* Antes de qualquer consulta, verifica a situação do aluno.
      #* Se o aluno estiver ativo, não pode agendar aula experimental pela IA.
      context = json.loads(self.user_context)
      situacao = context.get("aluno", {}).get("situacao", None)
      if isinstance(situacao, dict):
            situacao = situacao.get("codigo", None)

      #* Verificar se o usuário tem data de nascimento definida
      data_nascimento = context.get("aluno", {}).get("pessoa", {}).get("dataNasc", None)

      if not data_nascimento:
         return ("Para consultar as aulas disponíveis, preciso primeiro registrar sua data de nascimento. " +
                "Por favor, me informe sua data de nascimento para que eu possa usar a função save_user_birthdate " +
                "e depois mostrar as aulas adequadas para sua idade.")

      #* Calcular idade do usuário
      try:
         # Data pode vir em formato dd/mm/yyyy HH:MM:SS ou dd/mm/yyyy
         data_parts = data_nascimento.split(' ')[0].split('/')
         if len(data_parts) == 3:
            birth_date = datetime(int(data_parts[2]), int(data_parts[1]), int(data_parts[0]))
            today = datetime.now()
            user_age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
         else:
            user_age = 0
      except Exception as e:
         logger.error(f"Erro ao calcular idade: {e}")
         user_age = 0

      response = self.pit.check_classes_day(dia, mes, str(periodo))

      if isinstance(response, str) and "erro" in response.lower():
         return response

      #* Filtrar aulas disponíveis baseado na idade e situação do usuário
      filtered_response = dp.filter_available_classes(response.json(), user_age, situacao or "VI")

      register_indicator(
            identificador="informa_dados",
            id_empresa=self.id_empresa,
            indicador="aulas",
            telefone=self.telefone,
            nome=self._get_user_name()
      )
      
      processed_classes_data = dp.process_classes_data(filtered_response)
      if not processed_classes_data:
            return "Não há aulas disponíveis neste horário!"

      return processed_classes_data + instrucoes_para_ia

   @WorkersTracer(
      span_name_prefix=f"{__name__}.check_class_details",
      span_description="Verificando detalhes da aula",
      span_attributes={
         "aula": "aula",
         "dia": "dia",
         "mes": "mes",
         "id_empresa": "id_empresa",
      }
   )
   def check_class_details(self, aula: int, dia: int, mes: int) -> str:
      """
      Use esta função para obter detalhes específicos sobre UMA ÚNICA aula, **APENAS DEPOIS que o usuário escolher explicitamente uma opção** da lista de aulas que você forneceu.

      ## Fluxo de Trabalho Obrigatório
      1.  **Listar Aulas:** Primeiro, use `check_classes_day` para obter e mostrar ao usuário a lista de aulas disponíveis para um dia específico.
      2.  **Aguardar Escolha do Usuário:** Espere o usuário responder, indicando qual aula ele deseja.
      3.  **Verificar Detalhes:** **SOMENTE APÓS a escolha do usuário**, chame esta função (`check_class_details`) usando o código da aula que ele selecionou. Isso é crucial para verificar se a aula permite agendamento experimental e obter outros detalhes importantes.
      4.  **Agendar (se aplicável):** Se a verificação for positiva e o usuário confirmar o interesse, use `book_class` para finalizar o agendamento.

      **IMPORTANTE:** Não chame esta função para todas as aulas da lista. Chame-a apenas para a aula que o usuário escolheu. Não chame `book_class` sem antes usar esta função.

      :param int aula: O código da aula que o **usuário escolheu** da lista que você obteve com `check_classes_day`.
      :param int dia: O dia do mês que a aula ocorre.
      :param int mes: O mês que a aula ocorre.
      """
      response = self.pit.check_class_details(aula, dia, mes)

      try:
         class_details = response.json()

         if not class_details.get("content"):
            return "É necessário consultar corretamente as aulas com `check_classes_day` antes de ver os detalhes de uma aula!"
         
         msg = dp.process_class_data(class_details)
         
         #* Verifica o campo aulaCheia. Se verdadeiro, é uma aula. Se falso, é uma turma.
         #* Se o campo não existir, assume-se que é uma aula.
         isAula = class_details.get("content", {}).get("aulaCheia", True)
         
         #* Se for uma turma, verifica se permite aula experimental.
         if (not isAula):         
            permiteAulaExperimental = class_details.get("content", {}).get("permitirAulaExperimental", False)
            
            if (not permiteAulaExperimental):
               msg += "\n\nEssa turma **não permite o agendamento de aula experimental**. Informe ao usuário isso e **peça para ele escolher outra aula.**"
            else:
               msg += "\n\nSe foi chamado após o usuário escolher uma das aulas no contexto de agendar uma aula experimental, pergunte ao usuário se ele quer agendar essa aula."
         else:
            msg += "\n\nSe foi chamado após o usuário escolher uma das aulas no contexto de agendar uma aula experimental, pergunte ao usuário se ele quer agendar essa aula."

         return msg + \
            "\n- **NUNCA revele IDs:** Não mostre o código, ID da turma ou qualquer outro identificador numérico das aulas." + \
            "\n- **Use a linguagem correta:** Ao falar de uma aula disponível, diga 'A aula é às...' ou 'Temos às...'. A palavra 'agendada' é proibida neste momento." + \
            "\n- **Quando usar 'agendado(a)':** Use a palavra 'agendado(a)' somente na mensagem de confirmação, após a função `book_class` ser chamada e executada com sucesso."
      except:
         return response.text
      
   @WorkersTracer(
      span_name_prefix=f"{__name__}.book_call",
      span_description="Agendando ligação",
      span_attributes={ 
         "duvida": "duvida",
         "dia": "dia",
         "mes": "mes",
         "hora": "hora",
         "minuto": "minuto",
      }
   )
   def book_call(self, duvida: str, dia: int, mes: int, hora: int, minuto: int) -> str:
      """
      Esta função serve para agendar uma ligação com um consultor da academia, caso você não saiba responder a alguma dúvida do aluno.

      :param str duvida: A dúvida do aluno que você não consegue responder.
      :param int dia: O dia do mês que a ligação deve ocorrer.
      :param int mes: O mês que a ligação deve ocorrer.
      :param int hora: A hora que a ligação deve ocorrer.
      :param int minuto: O minuto que a ligação deve ocorrer.
      """

      user_data = json.loads(self.user_context)
      matricula = user_data.get("aluno", {}).get("matricula", None)
      codigo_lead = user_data.get("aluno", {}).get("codigo_lead", None)

      if not matricula and not codigo_lead:
         return (
            "Será necessário confirmar o CPF do aluno para agendar a ligação. "
            "Diga que ele precisa confirmar o CPF "
            "e o nome completo para agendar a ligação. "
            "Mas **não fale nada sobre cadastro**, "
            "você irá apenas usar a search_by_cpf para buscar o aluno."
         )

      response, status_code = self.pit.book_call(
         hora=hora,
         minuto=minuto,
         dia=dia,
         mes=mes,
         duvida=duvida,
         user_data=json.loads(self.user_context),
         fase=self.phase_id
      )

      if status_code == 200:
          register_indicator(
                "agendamento_ligacao",
                self.id_empresa,
                telefone=self.telefone,
                nome=self._get_user_name()
          )

      return response
   
   @WorkersTracer(
      span_name_prefix=f"{__name__}.book_visit",
      span_description="Agendando visita.",
      span_attributes={ 
         "objetivo": "objetivo",
         "dia": "dia",
         "mes": "mes",
         "hora": "hora",
         "minuto": "minuto",
      }
   )
   def book_visit(
         self, 
         objetivo: str, 
         dia: int, 
         mes: int, 
         hora: int, 
         minuto: int
    ) -> str:
      """
      Esta função agenda uma visita à academia. O cliente deve fornecer
      o horário e motivo da visita (que você pode determinar impliciatamente,
      de acordo com a conversa).

      :param str objetivo: Objetivo da visita.
      :param int dia: O dia do mês que a visita deve ocorrer.
      :param int mes: O mês que a visita deve ocorrer.
      :param int hora: A hora que a visita deve ocorrer.
      :param int minuto: O minuto que a visita deve ocorrer.
      """

      user_data = json.loads(self.user_context)
      matricula = user_data.get("aluno", {}).get("matricula", None)
      codigo_lead = user_data.get("aluno", {}).get("codigo_lead", None)

      logger.info(f"matricula: {matricula}, codigo_lead: {codigo_lead}")

      if not matricula and not codigo_lead:
         return (
            "Será necessário confirmar o CPF do aluno para agendar a ligação. "
            "Diga que ele precisa confirmar o CPF "
            "e o nome completo para agendar a ligação. "
            "Mas **não fale nada sobre cadastro**, "
            "você irá apenas usar a search_by_cpf para buscar o aluno."
         )

      result, status_code = self.pit.book_visit(
         hora=hora,
         minuto=minuto,
         dia=dia,
         mes=mes,
         objetivo=objetivo,
         user_data=json.loads(self.user_context),
         fase=self.phase_id
      )

      if status_code == 200:
          register_indicator(
                "agendamento_visita",
                self.id_empresa,
                telefone=self.telefone,
                nome=self._get_user_name()
            )

      return result

   @WorkersTracer(
      span_name_prefix=f"{__name__}.book_class",
      span_description="Agendando aula",
      span_attributes={
         "aula": "aula",
         "dia": "dia",
         "mes": "mes",
      }
   )
   def book_class(self, aula: int, dia: int, mes: int) -> str:
      """
      Você é capaz de agendar/reservar/marcar uma aula/aula experimental para um aluno por meio dessa função.
      Não é necessário fazer nenhum cadastro, o sistema irá gerenciar isso automaticamente.
      IMPORTANTE: Esta função deve ser chamada APENAS DEPOIS de você ter verificado a disponibilidade e os detalhes da aula usando a função `check_classes_day` e `check_class_details`.
      Não chame esta função sem uma verificação prévia. Chame essa função apenas UMA VEZ e aguarde a resposta.

      :param int aula: O codigo da aula que você deseja reservar.
      :param int dia: O dia do mês que a aula ocorre.
      :param int mes: O mês que a aula ocorre.
      """
      user_data = json.loads(self.user_context)
      matricula = user_data.get("aluno", {}).get("matricula", None)
      codigo_lead = user_data.get("aluno", {}).get("codigo_lead", None)

      if (not matricula) and codigo_lead:
         # Converte para visitante
         new_user_data = self.pit.convert_lead(codigo_lead)
         if new_user_data is None:
            return "Erro inesperado ao agendar aula."
         self.update_user_data(new_user_data, "book_class")
         matricula = new_user_data.get("aluno", {}).get("matricula", None)

      elif (not matricula) and (not codigo_lead):
         if not self.config["disable_cpf"]:
            connections.redis_client.set(
               f"pending_cpf_for_booking:{self.id_empresa}:{self.telefone}",
               "True",
               ex=15*60 # Expira em 15 minutos
            )
            
            return "Será necessário confirmar o CPF do aluno. Use este formato exato:\n\n" + \
                  "\"Ótima escolha! Para agendar a aula de [tipo de aula], preciso que você confirme seus dados:\n\n" + \
                  "Nome:\n" + \
                  "CPF:\n\n" + \
                  "Substitua \"[tipo de aula]\" pela aula que o usuário mencionou. Não adicione nenhuma outra informação ou frase." + \
                  "Mas **não fale nada sobre cadastro**, você irá apenas usar a `search_by_cpf` para buscar o aluno." + \
                  "Mesmo se não encontrá-lo, você ainda pode agendar a aula!" 
         else:
            nome = user_data.get("aluno", {}).get("pessoa", {}).get("nome", None)
            
            if (not nome) or ('desconhecido' in nome.lower()) or (nome == '') or ('user' in nome.lower()) or (len(nome.split(' ')) < 2):
               return "Será necessário confirmar o nome do aluno. " + \
                     "O nome do aluno não pode ser desconhecido e deve haver pelo menos dois nomes." + \
                     "Diga que ele precisa confirmar o nome para agendar a aula." + \
                     "Mas **não fale nada sobre cadastro**, você irá chamar a função `save_user_name` para salvar o nome do aluno."

            codigo_lead = self.pit.register_lead(
               nome=nome,
               telefone=self.telefone,
            )
            
            if not codigo_lead:
               return "Usuário não encontrado e não foi registrado como lead."
            
            logger.info(f"Usuário registrado como lead com sucesso: {codigo_lead}")
            id_conversa = connections.redis_client.get(
               f'current_conversation:{self.telefone}-{self.id_empresa}'
            )
            
            if id_conversa:
               id_conversa = id_conversa.decode('utf-8')
               connections.redis_client.set(
                  f"meta_diaria:{id_conversa}",
                  "True",
                  ex=8*60*60
               )
               
            user_data["aluno"] = {
               **user_data["aluno"],
               "codigo_lead": codigo_lead,
               "pessoa": {
                  **user_data.get("aluno", {}).get("pessoa", {}),
                  "cpf": None
               }
            }
            user_data["aluno"]["pessoa"]["nome"] = nome
            self.update_user_data(user_data, "book_class_registered_lead")
            
            new_user_data = self.pit.convert_lead(codigo_lead)
            if new_user_data is None:
               return "Erro inesperado ao agendar aula."
            self.update_user_data(new_user_data, "book_class")
            matricula = new_user_data.get("aluno", {}).get("matricula", None)

      book_msg = self.pit.book_class(aula, dia, mes, matricula, user_data)
      
      class_details = self.pit.check_class_details(aula, dia, mes)
      try:
         class_details = class_details.json()
      except:
         class_details = {}
         
      if "sucesso!" in book_msg:
         register_indicator("agendamento_aula", self.id_empresa, telefone=self.telefone)
         NotificationManager(
               self.id_empresa,
               class_attributes={
                   "telefone": self.telefone,
                   "is_group": self.is_group,
                   "id_session": self.id_session,
                   "origin": self.origin,
               }
           ).schedule(
           for_="book_class",
           kwargs={
               "book_msg": book_msg,
               "class_details": class_details,
               "mes": mes,
               "dia": dia,
               "name": self._get_user_name()
           }
         )
      elif "inesperado" in book_msg:
        whatsapp_link = generate_whatsapp_link(self.telefone, self.origin)
        booking_info = f"""⚠️ *Aviso de Agendamento* ⚠️

Não foi possível realizar o agendamento do(a) aluno(a) *{self._get_user_name()}*, às *{datetime.now().strftime('%H:%M')}*.

*Aula Solicitada:*
{dp.process_class_data(class_details, pretty=True)}

🚨 *Será necessário fazer a operação manualmente!*

*Link para contato com o aluno:*
{whatsapp_link}"""

        self.info_pending_forwarding.append(booking_info)
        book_msg = "Não foi possível realizar o agendamento automático " + \
            "mas um consultor já foi notificado!"

      return book_msg

   @WorkersTracer(
      span_name_prefix=f"{__name__}.generate_train",
      span_description="Gerando treino",
      span_attributes={
         "idade": "idade",
         "altura": "altura",
         "sexo": "sexo",
         "objetivo": "objetivo",
         "experiencia": "experiencia",
         "dias_de_treino": "dias_de_treino",
         "tempo_de_treino": "tempo_de_treino",
      }
   )
   def generate_train(self, idade: int, altura: int, sexo: str, objetivo: str, experiencia: str, dias_de_treino: str, tempo_de_treino: str) -> str:
      """
      Esta função é usada para gerar um treino personalizado com a nossa IA para o aluno com base em suas características pessoais e objetivos. O treino é adaptado às necessidades do aluno.
      
      :param int idade: A idade do aluno em anos.
      :param int altura: A altura do aluno em centímetros.
      :param str sexo: O sexo do aluno, pode ser {Masculino} ou {Feminino}.
      :param str objetivo: O objetivo principal do aluno, pode ser {Força}, {Hipertrofia} ou {Emagrecimento}.
      :param str experiencia: O nível de experiência do aluno, pode ser {Iniciante}, {Intermediário} ou {Avançado}.
      :param int dias_de_treino: O número de dias que o aluno deseja treinar por semana.
      :param int tempo_de_treino: O tempo disponível que o aluno terá para cada treino em minutos.
      """
      ## TODO: Usar o id do cliente correto
      ## TODO: Usar a condição atual do cliente -> Isso ainda não está sendo usado pela IA do treino
      client_id = 2029
      current_condition = "Treino entre 6 meses e 1 ano"
      url = "https://treino-por-ia-1060005216921.us-east1.run.app/generate-training-plan"
      headers = {
         "accept": "application/json",
         "Content-Type": "application/json"
      }
      request_data = {
         "client_id": client_id,
         "age": idade,
         "height": altura,
         "body_type": sexo,
         "goal": objetivo,
         "training_days": dias_de_treino,
         "training_time": tempo_de_treino,
         "experience_level": experiencia,
         "current_condition": current_condition
      }

      logger.info("Gerando treino...")

      response = req.post(url, headers=headers, json=request_data)

      return dp.process_train_data(response.json())[0]

   @WorkersTracer(
      span_name_prefix=f"{__name__}.add_previous_message",
      span_description="Adicionando mensagem anterior",
      span_attributes={
         "telefone": "telefone",
      }
   )
   def add_previous_message(self, telefone) -> None:
      previous_messages_ = self.bq.get_last_messages(limit=BUFFER_SIZE, telefone=telefone, roles_to_load_redis=ROLES_TO_KEEP_REDIS)

      if previous_messages_ is not None and type(previous_messages_) == DataFrame:
         for _, message in previous_messages_.iterrows():
               self.previous_messages.append({
                  "role": message["enviado_por"],
                  "content": message["mensagem"]
               })

   @WorkersTracer(
      span_name_prefix=f"{__name__}.execute",
      span_description="Executando"
   )
   def execute(self) -> str:
      response = self.get_response()

      #register_log()

      del self

      return response

   def update_user_data(self, data: dict, origin: str = None) -> None:
      if not isinstance(data, dict):
         raise TypeError("Os dados devem ser um dicionário.")
      if data == {}:
         return
      self.user_context = json.dumps(data)
      connections.redis_client.set(
        f"{self.telefone}-{self.id_empresa}",
        self.user_context,
        ex=8*60*60
      )
      task = {
         "type": "user",
         "id_empresa": self.id_empresa,
         "id_matriz": self.id_matriz,
         "data":
         {
            "telefone": self.telefone,
            "contexto": json.dumps(data),
            "fase": data.get("fase_crm", "LEADS_HOJE"),
            "origin_last_update": origin
         }
      }
      connections.redis_client.lpush('task_queue_bd_contexts', json.dumps(task))

   @WorkersTracer(
      span_name_prefix=f"{__name__}.save_messages",
      span_description="Salvando mensagens",
      span_attributes={
         "user_response": "user_response",
         "result": "result",
         "telefone": "telefone",
         "prompt_tokens": "prompt_tokens",
         "completion_tokens": "completion_tokens",
         "result_type": "result_type",
      }
   )
   def save_messages(self, user_response, result, telefone, prompt_tokens=0, completion_tokens=0, result_type="assistant"):
      if user_response:
         self.bq.save_message("user", user_response, telefone, roles_to_save_redis=ROLES_TO_KEEP_REDIS)
      if result:
         self.bq.save_message(result_type, result, telefone, model="gpt-4o-mini", prompt_tokens=prompt_tokens, completion_tokens=completion_tokens, roles_to_save_redis=ROLES_TO_KEEP_REDIS)

   @WorkersTracer(
      span_name_prefix=f"{__name__}.save_in_parallel",
      span_description="Salvando em paralelo",
   )
   def save_in_parallel(self, user_response, result, telefone, prompt_tokens=0, completion_tokens=0, result_type="assistant"):
      save_message_process = Process(target=self.save_messages, args=(user_response, result, telefone, prompt_tokens, completion_tokens, result_type))
      save_message_process.start()

   @WorkersTracer(
      span_name_prefix=f"{__name__}.create_function_message",
      span_description="Criando mensagem de função",
      span_attributes={
         "selected_tool_calls": "selected_tool_calls",
         "responses": "responses",
         "messages": "messages",
      }
   )
   def create_function_message(
            self,
            selected_tool_calls: list[ChatCompletionMessageToolCall],
            responses: dict[str, str],
            messages: list
   ) -> dict:
      print("Criando mensagem de função...")
      print(f"Respostas: {responses}")
      print(f"Chamadas de função selecionadas: {selected_tool_calls}")
      return self.client.chat.completions.create(
            model="gpt-4o-mini",
            messages=messages + [{
               "role": "assistant",
               "content": None,
                "tool_calls": [{
                    "id": call.id,
                    "type": "function",
                    "function": {
                        "name": call.function.name,
                        "arguments": call.function.arguments
                    }
                } for call in selected_tool_calls]
            }] + [{
                "role": "tool",
                "tool_call_id": call.id,
                "content": responses[call.id]
            } for call in selected_tool_calls],
            max_tokens=1200,
            stop=None,
            temperature=0.5
         )

   @WorkersTracer(
      span_name_prefix=f"{__name__}.process_tool_call",
      span_description="Processando chamada de função",
      span_attributes={
         "tool_calls": "tool_calls",
         "messages": "messages",
      }
   )
   def process_tool_call(
            self,
            tool_calls: list[ChatCompletionMessageToolCall],
            messages: list[dict]
    ) -> str:
      datas = []
      calls_to_regenerate = []
      responses = {}
      usage_tokens = 0
      completion_tokens = 0
      for tool_call in tool_calls:
          function_name = tool_call.function.name
          function_args = json.loads(tool_call.function.arguments)
          logger.info(f"Processando chamada de função: {function_name}")
          logger.info(f"Argumentos: {function_args}")
          data = None
          treino = None
          generate = True
          save = True
          if function_name == "get_additional_context":
             data = self.get_additional_context(**function_args)

          elif function_name == "register_origin":
             data = self.register_origin(**function_args)

          elif function_name == "save_additional_info":
             data = self.save_additional_info(**function_args)

          elif function_name == "transfer_user_departament":
             data = self.transfer_user_departament(**function_args)
          
          elif function_name == "search_by_cpf":
             data = self.search_by_cpf(**function_args)
          
          elif function_name == "save_user_name":
             data = self.save_user_name(**function_args)

          elif function_name == "save_user_empresa":
             data = self.save_user_empresa(**function_args)

          elif function_name == "save_user_level":
             data = self.save_user_level(**function_args)

          elif function_name == "check_classes_day":
             data = self.check_classes_day(**function_args)
          
          elif function_name == "check_class_details":
             data = self.check_class_details(**function_args)

          elif function_name == "book_class":
             data = self.book_class(**function_args)
          
          elif function_name == "book_call":
             data = self.book_call(**function_args)

          elif function_name == "book_visit":
             data = self.book_visit(**function_args)

          elif function_name == "save_user_birthdate":
             data = self.save_user_birthdate(**function_args)

          elif function_name == "warn_user":
             data = self.warn_user()

          elif function_name == "suggest_gyms_by_state_city":
             data = self.suggest_gyms_by_state_city(**function_args)
          
          elif function_name == "suggest_gyms_by_city":
             data = self.suggest_gyms_by_city(**function_args)
         
          elif function_name == "suggest_gyms":
             data = self.suggest_gyms(**function_args)

          elif function_name == "register_is_client_status":
                data = self.register_is_client_status(**function_args)

          elif function_name == "generate_train":
             treino = BaseResponse(self.generate_train(**function_args))     
             generate = False
             save = False

          elif function_name == "end_conversation":
             data = self.end_conversation()
             generate = False
          
          elif function_name == "dont_respond":
             data = self.dont_respond()
             generate = False
       
          if save:
             calls_to_regenerate.append(tool_call)
             responses[tool_call.id] = data
             self.save_in_parallel(None, f"Função chamada: {function_name}, argumentos: {function_args}, resposta: {data}", self.telefone, result_type="system")

          if generate:
                datas.append(data)

      if generate and datas != []:
         response = self.create_function_message(
            selected_tool_calls=calls_to_regenerate,
            responses=responses,
            messages=messages
         )
         result_message = response.choices[0].message
         usage_tokens = response.usage

      elif FINISH in datas:
         result_message = FINISH
         usage_tokens = None
      elif treino:
         result_message = treino
         usage_tokens = None
      else:
         result_message = STOP
         usage_tokens = None

      return result_message, usage_tokens

   @WorkersTracer(
      span_name_prefix=f"{__name__}.get_response",
      span_description="Obtendo resposta do GPT"
   )
   @retry()
   def get_response(self, user_response=None, ) -> tuple[str, dict]:
      if Config().isLoadTesting():
         return MockData.get_llm_message(), MockData.get_llm_infos()

      logger.info("Obtendo resposta do OpenAI...")
      messages = [
         self.content
      ] + self.previous_messages
      
      name = self._get_user_name()
      campanhas = self.bq.get_campaigns_context(None)
      is_campanha, is_template = False, False
      current_campanha = {}
      for campanha in campanhas:
         if not user_response: break
         if campanha.get("keyword").strip().lower() == user_response.strip().lower():
            data_inicio = campanha.get("data_inicio") if campanha.get("data_inicio")!="None" else datetime.now().strftime("%d/%m/%Y %H:%M:%S")
            data_inicio = datetime.strptime(data_inicio, "%d/%m/%Y %H:%M:%S")
            data_fim = campanha.get("data_fim") if campanha.get("data_fim") is not None and campanha.get("data_fim")!="None" else "31/12/3000 23:59:59"
            data_fim = datetime.strptime(data_fim, "%d/%m/%Y %H:%M:%S")

            valid_campanha = data_inicio <= datetime.now() <= data_fim
            if not valid_campanha: return "FINISH", {}

            is_campanha = True
            current_campanha = campanha.copy()
            is_template = current_campanha.get("is_template")
            instrucao = current_campanha.get("instrucao")
            register_indicator(
                identificador="mensagem_campanha",
                id_empresa=self.id_empresa,
                indicador=campanha.get("nome"),
                telefone=self.telefone,
                nome=name
            )
            
      if is_campanha and is_template:
         infos = {
            "prompt_tokens": 0, 
            "completion_tokens": 0,
            "n_chars": len(str(instrucao).replace(" ", "").replace("\n", "").strip()) if instrucao else 0,
            "model": None,
            "is_campanha": True,
            **current_campanha
         }
         return instrucao, infos
      
      elif is_campanha and not is_template:
         messages = [
            {"role": "system", "content": (
               f"Nome do usuário: {name}"
               f"SEU OBJETIVO É: {instrucao} **NÃO FALE coisas como claro, entendi, ok, tudo bem, Aqui está, etc... VÁ DIRETO PARA A MENSAGEM**\n"
            )}
         ] 

      elif user_response:
         messages.append({
            "role": "user",
            "content": user_response
         })

      response = self.client.chat.completions.create(
         model=OPENAI_MODEL,
         messages=messages,
         max_tokens=1200,
         stop=None,
         tools=self.function_descriptions,
         tool_choice="auto",
         temperature=0.5
      )

      result_message = response.choices[0].message

      usage_tokens = response.usage
      prompt_tokens_ = usage_tokens.prompt_tokens
      completion_tokens_ = usage_tokens.completion_tokens

      if result_message.tool_calls:
            tool_call_result = self.process_tool_call(result_message.tool_calls, messages)
            result_message = tool_call_result[0]
            if result_message == STOP:
                return None, {}
            elif result_message == FINISH:
                return FINISH, {}
            tool_call_usage_tokens = tool_call_result[1]
            if tool_call_usage_tokens:
                prompt_tokens_ += tool_call_usage_tokens.prompt_tokens
                completion_tokens_ += tool_call_usage_tokens.completion_tokens

      result = result_message.content

      infos = {
         "prompt_tokens": prompt_tokens_, 
         "completion_tokens": completion_tokens_,
         "n_chars": len(str(result).replace(" ", "").replace("\n", "").strip()) if result else 0,
         "model": OPENAI_MODEL,
         "is_campanha": is_campanha,
         **current_campanha
      }
      #self.save_in_parallel(user_response, result, self.telefone, prompt_tokens_, completion_tokens_)
      
      if result:
         result = dp.preprocess_text(result)

      return result, infos

   @WorkersTracer(
      span_name_prefix=f"{__name__}.__del__ - (Destrutor)",
      span_description="OpenAIResponseModule finalizado"
   )
   def __del__(self):
      logger.info("OpenAIResponseModule finalizado.")
      pass
