import os
import logging
import json
import redis
from openai import OpenAI
from google.oauth2 import service_account
from google.cloud import bigquery
from google.cloud import storage
import psycopg2
from psycopg2 import pool


with open("src/connections/keys.json") as f:
    keys = json.load(f)

logger = logging.getLogger("conversas_logger")

current_dir = os.path.dirname(__file__)
keys_path = os.path.join(current_dir, 'keys.json')
gbq_credentials_file_path = os.path.join(current_dir,'conversas-ai.json')
gcl_credentials_file_path = os.path.join(current_dir,'conversas-ai-buckets.json')

with open(keys_path) as f:
    keys = json.load(f)
    OPENAI_API_KEY = keys["OPEN_AI_API_KEY"]

with open(gbq_credentials_file_path, "r") as file:
    CREDENTIALS = json.load(file)

with open(os.path.join(gcl_credentials_file_path), "r") as file:
    CREDENTIALS_BUCKET = json.load(file)

REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
DATABASE = os.getenv("DATABASE", "postgres")


class SafePostgresConnectionPool:
    def __init__(self, pool: pool.ThreadedConnectionPool):
        self.pool: pool.ThreadedConnectionPool = pool
        # Intervalo em segundos para reutilizar conexões validadas
        self._validation_interval = 30

    def _validate_connection(self, conn: psycopg2.extensions.connection) -> bool:
        """
        Valida uma conexão verificando seu estado e executando uma query leve.
        """
        try:

            # Verificar se a conexão está fechada
            if conn.closed:
                logger.debug("Conexão fechada detectada.")
                return False

            # Testar a conexão com SELECT 1
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchall()
            logger.debug("Conexão validada com sucesso.")
            return True
        except psycopg2.OperationalError as e:
            logger.error(f"Erro ao validar conexão: {e}")
            return False
        except psycopg2.Error as e:
            logger.error(f"Erro inesperado ao validar conexão: {e}")
            return False

    def _get_connection_safely(self) -> psycopg2.extensions.connection:
        """
        Obtém uma conexão válida do pool, com retries em caso de falha.
        """
        max_retries = 3
        retries = 0

        while retries < max_retries:
            try:
                conn = self.pool.getconn()
                if self._validate_connection(conn):
                    return conn
                else:
                    logger.debug(
                        "Conexão inválida, fechando e tentando novamente.")
                    conn.close()
                    # Fechar conexão inválida
                    self.pool.putconn(conn, close=True)
            except psycopg2.OperationalError as e:
                logger.error(f"Erro ao obter conexão do pool: {e}")
            retries += 1

        raise Exception(
            f"Não foi possível obter uma conexão válida após {max_retries} tentativas"
        )

    def getconn(self) -> psycopg2.extensions.connection:
        """
        Retorna uma conexão válida do pool.
        """
        return self._get_connection_safely()

    def putconn(
        self, conn: psycopg2.extensions.connection, close=True
    ):
        """
        Devolve a conexão ao pool.
        """
        self.pool.putconn(conn, close=close)


class Connections:
    _instance = None

    def __init__(self) -> None:
        self.openai_client: OpenAI = OpenAI(api_key=OPENAI_API_KEY)
        logger.info("OpenAI client loaded!")
        credentials = service_account.Credentials.from_service_account_info(CREDENTIALS)
        self.bigquery_client: bigquery.Client = bigquery.Client(credentials=credentials, project=CREDENTIALS['project_id'])
        logger.info("BigQuery client loaded!")
        credentials_bucket = service_account.Credentials.from_service_account_info(CREDENTIALS)
        self.storage_client: storage.Client = storage.Client(credentials=credentials_bucket, project=CREDENTIALS_BUCKET['project_id'])
        logger.info("Google Storage client loaded!")
        self.redis_client: redis.Redis = redis.from_url(REDIS_URL)
        logger.info("Redis client loaded!")
        if DATABASE == "postgres":
            self.postgres_connection: SafePostgresConnectionPool = SafePostgresConnectionPool(
                pool.ThreadedConnectionPool(
                    minconn=int(os.getenv("POSTGRES_MIN_CONNECTIONS", 1)),
                    maxconn=int(os.getenv("POSTGRES_MAX_CONNECTIONS", 10)),
                    dbname=os.getenv("POSTGRES_DB"),
                    user=os.getenv("POSTGRES_USER"),
                    password=os.getenv("POSTGRES_PASSWORD"),
                    host=os.getenv("POSTGRES_HOST"),
                    port=os.getenv("POSTGRES_PORT", 5432),
                    keepalives=int(os.getenv("POSTGRES_KEEPALIVES", 1)),
                    keepalives_idle=int(os.getenv("POSTGRES_KEEPALIVES_IDLE", 120)),
                    keepalives_interval=int(os.getenv("POSTGRES_KEEPALIVES_INTERVAL", 30)),
                    keepalives_count=int(os.getenv("POSTGRES_KEEPALIVES_COUNT", 5))
                )
            )
            logger.info("PostgreSQL client loaded!")

    @classmethod
    def get_instance(cls) -> "Connections":
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
