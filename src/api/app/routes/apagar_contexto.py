import os
import json
from flask import Blueprint, request, jsonify, current_app

from src.data.bigquery_data import BigQueryData as bq
from src.api.app.auth.utils.auth_wrappers import authentication_required
from src.extras.util import parse_phone, RoutesTracing
from src.api.app.limiter.limiter import limiter
from src.api.app.exception.exception import BadRequest
from src.data.google_storage import Bucket

GCP_BIGQUERY_DATASET = os.getenv("GCP_BIGQUERY_DATASET")
DB_SCHEMA = os.getenv("DB_SCHEMA", "public")
BUCKET_NAME_CAMPANHA = os.getenv("BUCKET_NAME_CAMPANHA", "image_campanha")
DATABASE = os.getenv("DATABASE", "postgres")

apagar_contexto_academia_bp = Blueprint('apagar_contexto_academia', __name__)
apagar_contexto_planos_bp = Blueprint('apagar_contexto_planos', __name__)
apagar_contexto_fases_bp = Blueprint('apagar_contexto_fases', __name__)
apagar_contexto_turmas_bp = Blueprint('apagar_contexto_turmas', __name__)
apagar_contexto_produtos_bp = Blueprint('apagar_contexto_produtos', __name__)
apagar_contexto_personalidade_bp = Blueprint('apagar_contexto_personalidade', __name__)
apagar_contexto_aluno_bp = Blueprint('apagar_contexto_aluno', __name__)
apagar_strikes_aluno_bp = Blueprint('apagar_strikes_aluno', __name__)
apagar_contexto_campanha_bp = Blueprint('apagar_contexto_campanha', __name__)

@apagar_contexto_academia_bp.route('', methods=['DELETE'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="apagar_contexto_academia",
    capture_body_fields=["id_empresa"],
)
def apagar_contexto_academia(id_empresa: str = None):
    """
    Esta rota serve para apagar o contexto da academia, esses dados são usados para guiar a IA sobre informações específicas da empresa.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
        400:
            description: ID da academia não informado
            schema:
                id: error
    """

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    if DATABASE == "bigquery":
        query = f"DELETE FROM {GCP_BIGQUERY_DATASET}.contexto_academia WHERE id_empresa = '{id_empresa}'"
    elif DATABASE == "postgres":
        query = f"DELETE FROM {DB_SCHEMA}.contexto_academia WHERE id_empresa = :id_empresa"
    else:
        raise BadRequest("Erro ao apagar contexto da academia")

    current_app.redis_client.delete(f"gym_context-{id_empresa}")

    bq_ = bq(id_empresa=id_empresa)
    bq_._execute_query(query)

    return jsonify({"success": "success"}), 200

@apagar_contexto_planos_bp.route('', methods=['DELETE'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="apagar_contexto_planos",
    capture_body_fields=["id_empresa"],
)
def apagar_contexto_planos(id_empresa: str = None):
    """
    Esta rota serve para apagar o contexto dos planos da academia, esses dados servem para que a IA saiba quais são os planos disponíveis.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
        400:
            description: ID da academia não informado
            schema:
                id: error
    """

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    if DATABASE == "bigquery":
        query = f"DELETE FROM {GCP_BIGQUERY_DATASET}.contexto_planos WHERE id_empresa = '{id_empresa}'"
    elif DATABASE == "postgres":
        query = f"DELETE FROM {DB_SCHEMA}.contexto_planos WHERE id_empresa = :id_empresa"
    else:
        raise BadRequest("Erro ao apagar contexto de planos")

    bq_ = bq(id_empresa=id_empresa)
    bq_._execute_query(query)
    current_app.redis_client.delete(f"plans_context-{id_empresa}")

    return jsonify({"success": "success"}), 200

@apagar_contexto_fases_bp.route('', methods=['DELETE'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="apagar_contexto_fases",
    capture_body_fields=["id_empresa"]
)
def apagar_contexto_fases(id_empresa: str = None):
    """
    Esta rota serve para apagar o contexto das fases da academia, esses dados servem para que a IA saiba como falar com o usuário em cada fase.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: fase
        in: query
        type: string
        default: INICIAL
        required: false
        description: Código da fase. Se não informado, remove todas as fases
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
        400:
            description: ID da academia não informado
            schema:
                id: error
    """

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    phase = request.args.get('fase', None)
    if DATABASE == "bigquery":
        query = f"DELETE FROM {GCP_BIGQUERY_DATASET}.contexto_fases WHERE id_empresa = '{id_empresa}'"
        if phase is not None:
            query += f" AND codigo_fase = {phase}"
    elif DATABASE == "postgres":
        query = f"DELETE FROM {DB_SCHEMA}.contexto_fases WHERE id_empresa = :id_empresa"
        if phase is not None:
            query += f" AND codigo_fase = {phase}"
    else:
        raise BadRequest("Erro ao apagar contexto de fases")

    bq_ = bq(id_empresa=id_empresa)
    bq_._execute_query(query)
    current_app.redis_client.delete(f"phases_context-{id_empresa}")

    return jsonify({"success": "success"}), 200

@apagar_contexto_turmas_bp.route('', methods=['DELETE'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="apagar_contexto_turmas",
    capture_body_fields=["type", "id_empresa"],
)
def apagar_contexto_turmas(id_empresa: str = None):
    """
    Esta rota serve para apagar o contexto das turmas da academia, esses dados servem para que a IA saiba quais são as turmas disponíveis.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
        400:
            description: ID da academia não informado
            schema:
                id: error
    """

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    if DATABASE == "bigquery":
        query = f"DELETE FROM {GCP_BIGQUERY_DATASET}.contexto_turmas WHERE id_empresa = '{id_empresa}'"
    elif DATABASE == "postgres":
        query = f"DELETE FROM {DB_SCHEMA}.contexto_turmas WHERE id_empresa = :id_empresa"
    else:
        raise BadRequest("Erro ao apagar contexto de turmas")

    bq_ = bq(id_empresa=id_empresa)
    bq_._execute_query(query)
    current_app.redis_client.delete(f"classes_context-{id_empresa}")

    return jsonify({"success": "success"}), 200

@apagar_contexto_produtos_bp.route('', methods=['DELETE'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="apagar_contexto_produtos",
    capture_body_fields=["type", "id_empresa"],
)
def apagar_contexto_produtos(id_empresa: str = None):
    """
    Esta rota serve para apagar o contexto dos produtos da academia, esses dados servem para que a IA saiba quais são os produtos disponíveis.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
        400:
            description: ID da academia não informado
            schema:
                id: error
    """

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    if DATABASE == "bigquery":
        query = f"DELETE FROM {GCP_BIGQUERY_DATASET}.contexto_produtos WHERE id_empresa = '{id_empresa}'"
    elif DATABASE == "postgres":
        query = f"DELETE FROM {DB_SCHEMA}.contexto_produtos WHERE id_empresa = :id_empresa"
    else:
        raise BadRequest("Erro ao apagar contexto de produtos")

    bq_ = bq(id_empresa=id_empresa)
    bq_._execute_query(query)
    current_app.redis_client.delete(f"products_context-{id_empresa}")

    return jsonify({"success": "success"}), 200

@apagar_contexto_personalidade_bp.route('', methods=['DELETE'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="apagar_contexto_personalidade",
    capture_body_fields=["type", "id_empresa"],
)
def apagar_contexto_personalidade(id_empresa: str = None):
    """
    Esta rota serve para apagar o contexto da personalidade da academia, esse dado serve para que a IA saiba como falar com o usuário.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
        400:
            description: ID da academia não informado
            schema:
                id: error
    """

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    if DATABASE == "bigquery":
        query = f"DELETE FROM {GCP_BIGQUERY_DATASET}.contexto_personalidade WHERE id_empresa = '{id_empresa}'"
    elif DATABASE == "postgres":
        query = f"DELETE FROM {DB_SCHEMA}.contexto_personalidade WHERE id_empresa = :id_empresa"
    else:
        raise BadRequest("Erro ao apagar contexto de personalidade")

    bq_ = bq(id_empresa=id_empresa)
    bq_._execute_query(query)
    current_app.redis_client.delete(f"personality_context-{id_empresa}")
    current_app.redis_client.delete(f"voice_schedule:{id_empresa}")

    return jsonify({"success": "success"}), 200

@apagar_contexto_aluno_bp.route('', methods=['DELETE'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="apagar_contexto_aluno",
    capture_body_fields=["type", "id_empresa"],
)
def apagar_contexto_aluno(id_empresa: str = None):
    """
    Esta rota serve para apagar o contexto dos alunos da academia, esse dado serve para que a IA saiba quem é o usuário e tenha seu número de telefone.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: telefone
        in: query
        type: string
        default: '+5562988887777'
        required: true
        description: Telefone do aluno
      - name: reset_chat
        in: query
        type: boolean
        default: false
        required: false
        description: Se True, apaga o histórico de mensagens do chat
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
        400:
            description: ID da academia ou telefone não informado
            schema:
                id: error
    """

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    telefone = request.args.get('telefone', None)

    if not telefone:
        raise BadRequest("Telefone não informado")

    reset_chat = request.args.get('reset_chat', False)

    try:
        telefone = parse_phone(telefone)
    except (AttributeError, IndexError) as e:
        raise BadRequest("Telefone inválido") from e

    if DATABASE == "bigquery":
        query = f"DELETE FROM {GCP_BIGQUERY_DATASET}.contexto_usuario WHERE id_empresa = '{id_empresa}'"
        extra_query = f"AND telefone  = '{telefone}'"
    elif DATABASE == "postgres":
        query = f"DELETE FROM {DB_SCHEMA}.contexto_usuario WHERE id_empresa = :id_empresa"
        extra_query = f"AND telefone  = '{telefone}'"
    else:
        raise BadRequest("Erro ao apagar contexto de alunos")

    bq_ = bq(id_empresa=id_empresa)
    bq_._execute_query(query, extra_query=extra_query)
    current_app.redis_client.delete(f"{telefone}-{id_empresa}")

    if reset_chat:
        current_app.redis_client.delete(f"{telefone}-waiting_cpf")
        current_app.redis_client.delete(f"is_client:{id_empresa}:{telefone}")
        current_app.redis_client.delete(f"system_context:{id_empresa}:{telefone}")
        current_app.redis_client.delete(f"save_empresa:{id_empresa}-{telefone}")
        current_app.redis_client.delete(f"retry_contact:{telefone}-{id_empresa}")
        current_app.redis_client.delete(f"pendency_verification:{telefone}-{id_empresa}")
        current_app.redis_client.delete(f"current_conversation:{telefone}-{id_empresa}")
        current_app.redis_client.delete(f"id_empresa-{telefone}-{id_empresa}")
        current_app.redis_client.delete(f"CPF_search:{id_empresa}:{telefone}")
        current_app.redis_client.delete(f"memories:{id_empresa}:{telefone}")
        message_placeholder = [
            {
                "enviado_por": "assistant",
                "mensagem": "...",
            }
        ]
        current_app.redis_client.delete(f"last_messages-{telefone}-{id_empresa}")
        current_app.redis_client.set(
            f"last_messages-{telefone}-{id_empresa}",
            json.dumps(message_placeholder),
            ex=24*60*60
        )

    return jsonify({"success": "success"}), 200

@apagar_strikes_aluno_bp.route('', methods=['DELETE'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="apagar_strikes_aluno",
    capture_body_fields=["type", "id_empresa"],
)
def apagar_strikes_aluno(id_empresa: str = None):
    """
    Esta rota serve para apagar os strikes dos alunos da academia.
    ---
    tags:
        - Segurança
    security: 
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: telefone
        in: query
        type: string
        default: '+5562988887777'
        required: true
        description: Telefone do aluno
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
        400:
            description: ID da academia ou telefone não informado
            schema:
                id: error
    """

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    telefone = request.args.get('telefone', None)

    if not telefone:
        raise BadRequest("Telefone não informado")

    try:
        telefone = parse_phone(telefone)
    except (AttributeError, IndexError) as e:
        raise BadRequest("Telefone inválido") from e

    current_app.redis_client.delete(f"strikes:{telefone}-{id_empresa}")

    return jsonify({"success": "success"}), 200


@apagar_contexto_campanha_bp.route('', methods=['DELETE'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="apagar_contexto_campanha",
    capture_body_fields=["type", "id_empresa"],
)
def apagar_contexto_campanha(id_empresa: str = None):
    """
    Esta rota serve para apagar uma campanha da academia
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: id_campanha
        in: query
        type: string
        default: None
        example: dd9ef247-8500-4c56-9be7-c38f24c7a853a
        required: true
        description: id da campanha
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
        400:
            description: Erro ao apagar campanha
            schema:
                id: error
    """

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    id_campanha = request.args.get('id_campanha', None)
    if not id_campanha:
        raise BadRequest("Nome da campanha não informado")

    bucket = Bucket(BUCKET_NAME_CAMPANHA)
    bucket.delete(f"{id_empresa}/{id_campanha}")

    if DATABASE == "bigquery":
        query = f"DELETE FROM {GCP_BIGQUERY_DATASET}.contexto_campanhas WHERE id_empresa = '{id_empresa}' AND id_campanha = '{id_campanha}'"
    elif DATABASE == "postgres":
        query = f"DELETE FROM {DB_SCHEMA}.contexto_campanhas WHERE id_empresa = '{id_empresa}' AND id_campanha = '{id_campanha}'"
    else:
        raise BadRequest("Erro ao apagar campanha")

    bq_ = bq(id_empresa=id_empresa)
    result = bq_._execute_query(query)

    if current_app.redis_client.exists(f"contexto_campanhas:{id_empresa}"):
        campanhas = json.loads(current_app.redis_client.get(f"contexto_campanhas:{id_empresa}"))
        available_campanhas = [x for x in campanhas if x["id_campanha"] != id_campanha]
        current_app.redis_client.set(
            f"contexto_campanhas:{id_empresa}",
            json.dumps(available_campanhas),
            ex=8*60*60
        )

    if result.num_dml_affected_rows == 0:
        raise BadRequest(f"Nenhuma campanha encontrada para esse id_empresa com esse id_campanha: {id_campanha}")

    return jsonify({"success": f"success. id_campanha {id_campanha} deletado."}), 200
