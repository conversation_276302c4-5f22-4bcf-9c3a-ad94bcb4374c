import pandas as pd
import json
import logging
import os
from datetime import datetime

from flask import Blueprint, request, jsonify, current_app

from src.api.app.auth.utils.auth_wrappers import verify_origin
from src.data.bigquery_data import BigQueryData as bq
from src.extras.util import validar_token, RoutesTracing

from src.extras.task_adapter import TaskAdapter
from src.integrations.gymbot.tools.integration_tools import GymbotIntegrationTools
from src.api.app.shared.processar_mensagem_recebida import processar_e_rotear_mensagem

logger = logging.getLogger("conversas_logger")

gymbot_webhook_bp = Blueprint('gymbot_webhook', __name__)
atualizar_token_bp = Blueprint('atualizar_token', __name__)
consultar_token_bp = Blueprint('consultar_token', __name__)
departamento_bp = Blueprint('departamento', __name__)

departamento_gymbot_bp = Blueprint('departamento_gymbot', __name__)
departamento_instrucoes_bp = Blueprint('departamento_instrucoes', __name__)

GCP_BIGQUERY_DATASET = os.getenv("GCP_BIGQUERY_DATASET")
DATABASE = os.getenv("DATABASE", "postgres")

@gymbot_webhook_bp.route('', methods=['POST'])
@verify_origin()
@RoutesTracing(
    span_name_prefix="gymbot_webhook",
    capture_body_fields=["id_empresa"],
)
def gymbot_webhook(origin="Z-API"):
    logger.info(f"[WEBHOOK] Origin: {origin}")
    try:
        id_empresa = request.args["id_empresa"]
        data = request.json

        if not data:
            return json.dumps({"error": "No data received"}), 400
        
        match data['eventType']:

            case "SESSION_COMPLETE": 
                logger.info(f"[SESSION_COMPLETE] Atendimento concluido, tranferindo para conversa de volta para IA")
                logger.info(f"[SESSION_COMPLETE] Data: {json.dumps(data, indent=2)}")
                task = {
                    "id_empresa": id_empresa,
                    "contactId": data['content']['contactId'],
                }

                return jsonify({
                    "success": "success",
                }), 200

            
            case "MESSAGE_SENT":
                # Precisa fazer isso aqui porque o gymbot tá mandando o evento MESSAGE_SENT com a mesma mensagem 3 vezes!
                last_id_mensage_sent = current_app.redis_client.get(f"gymbot:last_id_mensage_sent:{id_empresa}")
                if last_id_mensage_sent and  data["content"]["id"] == last_id_mensage_sent.decode("utf-8"):
                    logger.info(f"[MESSAGE_SENT] ID igual! Essa mensagem já foi enviada antes.")
                    return jsonify({
                        "success": "Mensagem já foi procesada antes.",
                    }), 202
                current_app.redis_client.set(
                    f"gymbot:last_id_mensage_sent:{id_empresa}",
                    str(data["content"]["id"]),
                    ex=24*60*60
                )

                logger.info(f"[MESSAGE_SENT] Data: {json.dumps(data, indent=2)}")
                
                # Inclusão no message_status

                task_update = {
                    "provedor": "gymbot",
                    "id_empresa": id_empresa,
                    "data": {
                        "status_gymbot": "SENT",
                        **data
                    }
                }
                current_app.redis_client.lpush('save_status', json.dumps(task_update))

                #Fim message_status

                task = {
                    "id_empresa" : id_empresa,
                    "data" : data,
                    "sessionId": None,
                    "origin": origin
                }

                task, msg = TaskAdapter(task, origin, id_empresa).new_task
                logger.info(f"\n\ntask: {json.dumps(task, indent=2)}")
                if not task:

                    return jsonify({
                        "success": msg,
                    }), 205

                if not task["canAnswer"]:
                    logger.info(f"[MESSAGE_SENT] Mensagem enviada para o GymBot")
                    current_app.redis_client.lpush('message_sent_gymbot', json.dumps(task))

                    return jsonify({
                        "success": "Essa mensagem foi de outro departamento",
                    }), 200

                else:

                    return jsonify({
                        "success": "Essa mensagem foi gerada pela como resposta pela IA",
                    }), 201

            case "MESSAGE_RECEIVED":
                logger.info(f"[MESSAGE_RECEIVED] Mensagem recebida")
                logger.info(f"[MESSAGE_RECEIVED] Data: {json.dumps(data, indent=2)}")

                # Função específica de pós-roteamento para este webhook
                def salvar_status_gymbot(task_processada, id_empresa):
                    logger.info("Executando ação pós-roteamento: salvar_status_gymbot")
                    task_update = {
                        "provedor": "gymbot",
                        "id_empresa": id_empresa,
                        "data": {
                            "status_gymbot": "RECEIVED",
                            **task_processada.get("data", {})
                        }
                    }
                    current_app.redis_client.lpush('save_status', json.dumps(task_update))

                # Task inicial (mínima)
                task_inicial = {
                    "id_empresa": id_empresa,
                    "data": data,
                    "sessionId": None,
                    "origin": origin,
                    "momento_recebimento": datetime.now().timestamp()
                }

                # Chamada para a função compartilhada
                _, _ = processar_e_rotear_mensagem(
                    task_inicial=task_inicial,
                    redis_client=current_app.redis_client,
                    logger=logger,
                    executar_pos_roteamento=salvar_status_gymbot
                )

                return jsonify({
                    "success": "success",
                }), 200
            
            case "MESSAGE_UPDATED":
                logger.info(f"[MESSAGE_UPDATED] Mensagem lida")
                logger.info(f"[MESSAGE_UPDATED] Data: {json.dumps(data, indent=2)}")

                task_update = {
                    "provedor": "gymbot",
                    "id_empresa": id_empresa,
                    "data": {
                        "status_gymbot": "READ",
                        **data
                    }
                }
                current_app.redis_client.lpush('save_status', json.dumps(task_update))

                return jsonify({
                    "success": "success",
                }), 200

            case "SESSION_NEW":
                logger.info(f"[SESSION_NEW] Mensagem lida")
                logger.info(f"[SESSION_NEW] Data: {json.dumps(data, indent=2)}")

                if content := data.get("content"):
                    task = {
                        "id_empresa": id_empresa,
                        "data": content,
                        "sessionId": None,
                        "origin": origin,
                        "momento_recebimento": datetime.now().timestamp()
                    }

                    current_app.redis_client.lpush('transfer_conversation', json.dumps(task))

                return jsonify({
                    "success": "success",
                }), 200

        logger.info(f"Evento não tratado: {data['eventType']}")
        return jsonify({
            "success": "Evento não suportado",
        }), 206

    except Exception as e:
        logger.error(f"Error: {e}")
        return jsonify({"error": str(e)}), 208


@atualizar_token_bp.route('', methods=['POST'])
@RoutesTracing(
    span_name_prefix="atualizar_token",
    capture_body_fields=["id_empresa"]
)
def atualizar_token():
    """
    Esta rota salva as informações de tokens necessários para integração e cria webhook na plataforma no bigquery.
    ---
    tags:
        - Integração
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: canal
        in: query
        type: string
        default: gymbot
        required: true
        description: Nome do canal de chatbot
      - name: body
        in: body
        required: true
        schema:
          id: content
          required:
            - token
          properties:
            token:
                type: string
                description: Token do GymBot
                example: pn_xxxxxxxxxxxxxxxxx
            save_webhook:
                type: boolean
                description: Cria o webhook na plataforma
                example: true
            webhook_enabled:
                type: boolean
                description: indica se o webhook deve estar ativo ou não quando criado
                example: false
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
                type: object
                properties:
                    success:
                        type: string
                        example: success
        400:
            description: ID da academia não informado
            schema:
                id: error
                type: object
                properties:
                    error:
                        type: string
                        example: ID da academia não informado
    """
    data = request.json 
    id_empresa = request.args["empresa"]
    canal = request.args["canal"].lower().strip()

    if not data:
        return json.dumps({"error": "No data received"}), 400

    token = data.get("token")
    if token is None:
        return json.dumps({"error": "token não pode ser nulo"}), 400
    
    if not validar_token(token):
        return json.dumps({"error": "token não está no formato correto"}), 400

    bq_ = bq(id_empresa)
    logger.info(f"Validando se token {token} já existe no BQ...")
    if DATABASE == "bigquery":
        table_id = f"{GCP_BIGQUERY_DATASET}.gymbot_tokens"
    else:
        table_id = f"gymbot_tokens"
    exists_token = bq_._check_if_row_exists(table_id, "token", token)
    
    if exists_token:
        return jsonify({"error": "Token já cadastrado"}), 400

    try:
        if canal == "gymbot":
            data = request.json
            id_empresa = request.args.get('empresa')

            task = {
                "type" : "gymbot_token",
                "id_empresa" : id_empresa,
                "data" : data
            }

            current_app.redis_client.lpush('task_queue_bd_contexts', json.dumps(task))
            return jsonify({"success": "success"}), 200
        else:
            raise Exception(f"canal {canal} ainda não suportado")
    except Exception as e:
        return jsonify({"error": f"GET " + str(e)}), 500

@consultar_token_bp.route('', methods=['GET'])
@RoutesTracing(
    span_name_prefix="consultar_token",
    capture_body_fields=["id_empresa"]
)
def consultar_token():
    """
    Esta rota consulta as informações de tokens necessários para integração no bigquery.
    ---
    tags:
        - Integração
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: canal
        in: query
        type: string
        default: gymbot
        required: true
        description: Nome do canal de chatbot
    responses:
        200:
            description: Retorna o token
            schema:
                type: object
                properties:
                    token:
                        type: string
                        example: pn_xxxxxxxxxxxxxxxxx
        500:
            description: Retorna erro
            schema:
                type: object
                properties:
                    error:
                        type: string
                        example: A empresa não possui token cadastrado
    """

    id_empresa = request.args["empresa"]
    canal = request.args["canal"].lower().strip()

    try:
        if canal == "gymbot":
            bq_ = bq(id_empresa)
            token = bq_.get_gymbot_token()
            if token:
                return jsonify({"token": token})
            raise Exception(f"A empresa não possui token cadastrado")
        else:
            raise Exception(f"canal {canal} ainda não suportado")
        
    except Exception as e:
        return jsonify({"error": f"GET " + str(e)}), 500


# Rota para listar os departamentos existentes
@departamento_bp.route('', methods=['GET'])
@RoutesTracing(
    span_name_prefix="listar_departamentos",
    capture_body_fields=["id_empresa"]
)
def listar_departamentos():
    """
    Esta rota retorna a lista de departamentos existentes direto do gymbot.
    ---
    tags:
        - Integração
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: canal
        in: query
        type: string
        default: gymbot
        required: true
        description: Nome do canal de chatbot
    responses:
        200:
            description: Lista com nome e ID dos departamentos.
            schema:
                type: object
                properties:
                    departaments:
                        type: array
                        items:
                            type: object
                            properties:
                                name:
                                    type: string
                                    description: Nome do departamento
                                    example: Financeiro
                                id:
                                    type: string
                                    description: Identificador único do departamento
                                    example: 729e9f85-3f3e-472b-b92d-d720f4babc37
    """
    try:
        id_empresa = request.args["empresa"]
        canal = request.args["canal"].lower().strip()
        
        if canal == "gymbot":
            gbit = GymbotIntegrationTools(id_empresa)
            status, departaments = gbit.get_departamento()
            if status == 200:
                return jsonify([{"name": d["name"], "id": d["id"]} for d in departaments])
            if status in [400, 401]:
                return jsonify({"error": f"Erro ao autenticar com gymbot {canal}"}), 400
            return jsonify({"error": f"Erro ao buscar departamentos {canal}"}), 500
        else:
            raise Exception(f"canal: {canal} ainda não suportado")

    except Exception as e:
        return jsonify({"error": f"GET " + str(e)}), 500
    
# Rota para cadastrar instruções para a IA decidir transferências para o departamento
@departamento_bp.route('', methods=['POST'])
@RoutesTracing(
    span_name_prefix="atualizar_departamento",
    capture_body_fields=["id_empresa"]
)
def atualizar_departamento():
    """
    Esta rota atualizar instruções para a IA decidir quando transferir para o departamento no bigquery.
    ---
    tags:
        - Integração
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: canal
        in: query
        type: string
        default: gymbot
        required: true
        description: Nome do canal de chatbot
      - name: body
        in: body
        required: true
        schema:
            type: object
            properties:
                departament_descriptions:
                    type: array
                    description: Lista de configurações para a IA decidir transferências
                    items:
                        type: object
                        properties:
                            id:
                                type: string
                                description: Identificador único do departamento
                                example: 729e9f85-3f3e-472b-b92d-d720f4babc37
                            descricao:
                                type: string
                                description: Instruções para a IA saber quando transferir para o departamento
                                example: Transfira para este departamento quando o usuário quiser saber sobre pagamentos.
    responses:
        201:
            description: Instruções cadastradas com sucesso.
            schema:
                type: object
                properties:
                    message:
                        type: string
                        example: "Instruções cadastradas com sucesso!"
        400:
            description: Requisição inválida, faltando parâmetros obrigatórios.

    """
    try:
        id_empresa = request.args["empresa"]
        canal = request.args["canal"].lower().strip()
        if canal == "gymbot":
            data: dict = request.json

            if "departament_descriptions" in data.keys():
                task = {
                    "type": "gymbot_departament",
                    "id_empresa": id_empresa,
                    "data": data
                }
            else:
                task = {
                    "type": "gymbot_departament",
                    "id_empresa": id_empresa,
                    "data":{
                        "departament_descriptions": [
                            data
                        ]
                    } 
                }

            current_app.redis_client.lpush('task_queue_bd_contexts', json.dumps(task))
            
            task_syncronize = {"type": "gymbot_syncronize_departaments", "id_empresa": id_empresa, "data":{}}
            current_app.redis_client.lpush('task_queue_bd_contexts', json.dumps(task_syncronize))
            return jsonify({"success": "success"}), 200
        else:
            raise Exception(f"canal {canal} ainda não suportado")

    except Exception as e:
        return jsonify({"error": f"POST " + str(e)}), 500
    




# Rota para listar os departamentos existentes
@departamento_gymbot_bp.route('', methods=['GET'])
@RoutesTracing(
    span_name_prefix="listar_departamentos",
    capture_body_fields=["id_empresa"]
)
def listar_departamentos_v2():
    """
    Esta rota retorna a lista de departamentos existentes direto do gymbot.
    ---
    tags:
        - Integração
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: canal
        in: query
        type: string
        default: gymbot
        required: true
        description: Nome do canal de chatbot
    responses:
        200:
            description: Lista com nome e ID dos departamentos.
            schema:
                type: object
                properties:
                    departaments:
                        type: array
                        items:
                            type: object
                            properties:
                                name:
                                    type: string
                                    description: Nome do departamento
                                    example: Financeiro
                                id:
                                    type: string
                                    description: Identificador único do departamento
                                    example: 729e9f85-3f3e-472b-b92d-d720f4babc37
    """
    try:
        id_empresa = request.args["empresa"]
        canal = request.args["canal"].lower().strip()
        
        if canal == "gymbot":
            gbit = GymbotIntegrationTools(id_empresa)
            status, departaments = gbit.get_departamento()
            if status == 200:
                return jsonify([{"name": d["name"], "id": d["id"]} for d in departaments])
            if status == 400:
                return jsonify({"error": f"Erro ao receber dados gymbot {canal}"}), 400
            return jsonify({"error": f"Erro ao buscar departamentos {canal}"}), 500
        else:
            raise Exception(f"canal: {canal} ainda não suportado")

    except Exception as e:
        return jsonify({"error": f"GET " + str(e)}), 500


# Rota para cadastrar instruções para a IA decidir transferências para o departamento
@departamento_instrucoes_bp.route('', methods=['POST'])
@RoutesTracing(
    span_name_prefix="atualizar_departamento_v2",
    capture_body_fields=["id_empresa"]
)
def atualizar_departamento_instrucoes():
    """
    Esta rota atualizar instruções para a IA decidir quando transferir para o departamento no bigquery.
    ---
    tags:
        - Integração
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: canal
        in: query
        type: string
        default: gymbot
        required: true
        description: Nome do canal de chatbot
      - name: body
        in: body
        required: true
        schema:
            type: object
            properties:
                departament_descriptions:
                    type: array
                    description: Lista de configurações para a IA decidir transferências
                    items:
                        type: object
                        properties:
                            id:
                                type: string
                                description: Identificador único do departamento
                                example: 729e9f85-3f3e-472b-b92d-d720f4babc37
                            descricao:
                                type: string
                                description: Instruções para a IA saber quando transferir para o departamento
                                example: Transfira para este departamento quando o usuário quiser saber sobre pagamentos.
    responses:
        201:
            description: Instruções cadastradas com sucesso.
            schema:
                type: object
                properties:
                    message:
                        type: string
                        example: "Instruções cadastradas com sucesso!"
        400:
            description: Requisição inválida, faltando parâmetros obrigatórios.

    """
    try:
        id_empresa = request.args["empresa"]
        canal = request.args["canal"].lower().strip()
        if canal == "gymbot":
            data: dict = request.json

            if "departament_descriptions" in data.keys():
                task = {
                    "type": "gymbot_departament",
                    "id_empresa": id_empresa,
                    "data": data
                }
            else:
                task = {
                    "type": "gymbot_departament",
                    "id_empresa": id_empresa,
                    "data":{
                        "departament_descriptions": [
                            data
                        ]
                    } 
                }

            current_app.redis_client.lpush('task_queue_bd_contexts', json.dumps(task))
            
            task_syncronize = {"type": "gymbot_syncronize_departaments", "id_empresa": id_empresa, "data":{}}
            current_app.redis_client.lpush('task_queue_bd_contexts', json.dumps(task_syncronize))
            return jsonify({"success": "success"}), 200
        else:
            raise Exception(f"canal {canal} ainda não suportado")

    except Exception as e:
        return jsonify({"error": f"POST " + str(e)}), 500
    

@departamento_instrucoes_bp.route('', methods=['GET'])
@RoutesTracing(
    span_name_prefix="consultar_departamento_instrucoes",
    capture_body_fields=["id_empresa"]
)
def consultar_departamento_instrucoes():
    """
    Esta rota consulta as instruções para a IA decidir quando transferir para o departamento no bigquery.
    ---
    tags:
        - Integração
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: canal
        in: query
        type: string
        default: gymbot
        required: true
        description: Nome do canal de chatbot
    responses:
        200:
            description: Retorna as instruções cadastradas.
            schema:
                type: object
                properties:
                    departament_descriptions:
                        type: array
                        items:
                            type: object
                            properties:
                                id:
                                    type: string
                                    description: Identificador único do departamento
                                    example: 729e9f85-3f3e-472b-b92d-d720f4babc37
                                descricao:
                                    type: string
                                    description: Instruções para a IA saber quando transferir para o departamento
                                    example: Transfira para este departamento quando o usuário quiser saber sobre pagamentos.
    """
    try:
        id_empresa = request.args["empresa"]
        canal = request.args["canal"].lower().strip()
        
        if canal == "gymbot":
            gbit = GymbotIntegrationTools(id_empresa)
            status, departaments = gbit.get_departamento()
            departaments_gymbot = pd.DataFrame(
                [[d["id"], d["name"]] for d in departaments],
                columns=["id", "name"]
            )
            if status != 200: raise Exception(f"Erro ao buscar departamentos {canal}")
            bq_ = bq(id_empresa)
            departaments_descriptions = pd.DataFrame(bq_.get_departament_descriptions(), columns=['id_departamento', 'departamento', 'descricao'])
            departaments_descriptions = departaments_gymbot.merge(
                departaments_descriptions,
                how="inner",
                left_on="id",
                right_on="id_departamento"
            )
            return jsonify(departaments_descriptions[["id_departamento", "departamento", "descricao"]].to_dict(orient="records"))

        else:
            raise Exception(f"canal {canal} ainda não suportado")

    except Exception as e:
        return jsonify({"error": f"GET " + str(e)}), 500
    

@departamento_instrucoes_bp.route('', methods=['DELETE'])
@RoutesTracing(
    span_name_prefix="deletar_departamento_instrucoes",
    capture_body_fields=["id_empresa"]
)
def deletar_departamento_instrucoes():
    """
    Esta rota deleta as instruções para a IA decidir quando transferir para o departamento no bigquery.
    ---
    tags:
        - Integração
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: canal
        in: query
        type: string
        default: gymbot
        required: true
        description: Nome do canal de chatbot
      - name: body
        in: body
        required: true
        schema:
            type: object
            properties:
                id:
                    type: array
                    items:
                        type: string
                        description: Identificador único do departamento a ser deletado
                        example: 729e9f85-3f3e-472b-b92d-d720f4babc37
    responses:
        200:
            description: Instruções deletadas com sucesso.
            schema:
                type: object
                properties:
                    message:
                        type: string
                        example: "Instruções deletadas com sucesso!"
    """
    try:
        id_empresa = request.args["empresa"]
        canal = request.args["canal"].lower().strip()
        
        if canal == "gymbot":
            data = request.json

            if "id" not in data.keys():
                return jsonify({"error": "o campo id é obrigatório"}), 400

            if not isinstance(data["id"], list):
                return jsonify({"error": "o campo id deve ser uma lista"}), 400
            
            task = {
                "type": "gymbot_departament_delete",
                "id_empresa": id_empresa,
                "data": data
            }

            current_app.redis_client.lpush('task_queue_bd_contexts', json.dumps(task))
            
            return jsonify({"success": "success"}), 200

        else:
            raise Exception(f"canal {canal} ainda não suportado")

    except Exception as e:
        return jsonify({"error": f"DELETE " + str(e)}), 500
