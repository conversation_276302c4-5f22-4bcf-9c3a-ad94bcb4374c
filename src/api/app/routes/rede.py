import os
import logging
import json
from flask import Blueprint, request, jsonify, current_app

from src.data.bigquery_data import BigQueryData as bq, get_from_empresa
from src.api.app.auth.utils.auth_wrappers import authentication_required
from src.extras.util import parse_phone, RoutesTracing
from src.api.app.limiter.limiter import limiter
from src.api.app.exception.exception import BadRequest

DATABASE = os.getenv("DATABASE", "postgres")
GCP_BIGQUERY_DATASET = os.getenv("GCP_BIGQUERY_DATASET")
DB_SCHEMA = os.getenv("DB_SCHEMA", "public")
BUCKET_NAME_CAMPANHA = os.getenv("BUCKET_NAME_CAMPANHA", "image_campanha")

logger = logging.getLogger("conversas_logger")

rede_bp = Blueprint('/rede', __name__)

@rede_bp.route('/<empresa>', methods=['GET'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="rede",
    capture_body_fields=["empresa"],
)
def consultar_rede(empresa: str):
    """
    Esta rota serve para buscar todas as academias de uma rede.
    ---
    tags:
        - Rede
    parameters:
      - name: empresa
        in: path
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
    security: 
        - Bearer: []
    responses:
        200:
            description: Retorna os membros de uma rede de empresas
            schema:
                id: Rede
                properties:
                    status:
                        type: string
                        description: Status da requisição
                    content:
                        type: object
                        properties:
                            message:
                                type: string
                                description: Mensagem de sucesso
                            chaveMatriz:
                                type: string
                                description: ID da matriz
                            tipo:
                                type: string
                                description: Tipo de empresa (matriz ou filial)
                            rede:
                                type: array
                                items:
                                    type: object
                                    properties:
                                        id_empresa:
                                            type: string
                                            description: ID da empresa
                                        nome_empresa:
                                            type: string
                                            description: Nome da empresa
        400:
            description: "{empresa} não é uma empresa de rede, é individual."
            schema:
                properties:
                    status:
                        type: string
                        description: Status da requisição
                    context:
                        type: string
                        description: Status da requisição
    """
    if not empresa:
        raise BadRequest("ID da academia não informado")
    
    bq_ = bq(id_empresa=empresa)
    gym_context = bq_.get_gym_context()

    chaveMatriz = gym_context.get("chaveMatriz")
    if not chaveMatriz:
        return jsonify({
            "status": "failure",
            "content":{
                "message": f"{empresa} não é uma empresa de rede, é individual.",
            }
        }), 400

    bq_matriz = bq(id_empresa=chaveMatriz)
    chain_context = bq_matriz.get_chain_context()
    if not chain_context:
        return jsonify({
            "status": "failure",
            "content":{
                "message": f"{empresa} é uma empresa de rede. Mas, a rede não foi encontrada.",
                "chaveMatriz": chaveMatriz,
                "tipo": "filial" if chaveMatriz != empresa else "matriz",
                "rede": [],
            }
        }), 404

    return jsonify({
        "status": "success",
        "content":{
            "message": f"Rede encontrada para {empresa}",
            "chaveMatriz": chaveMatriz,
            "tipo": "filial" if chaveMatriz != empresa else "matriz",
            "rede": chain_context,
        }
    }), 200

@rede_bp.route('/matriz/<matriz>', methods=['DELETE'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="rede",
    capture_body_fields=["matriz"],
    capture_query_params=["matriz"],
)
def apagar_matriz_rede(matriz: str):
    """
    Esta rota serve para apagar uma rede completamente por meio da chave-unidade da matriz.
    ---
    tags:
        - Rede
    parameters:
      - name: matriz
        in: path
        type: string
        default: chave-1
        required: true
        description: ID da academia, no formato chave-id
    security: 
        - Bearer: []
    responses:
        200:
            description: Retorna os membros de uma rede de empresas
            schema:
                properties:
                    content:
                        properties:
                            message:
                                type: string
                                example: "Rede da matriz {matriz} apagada com sucesso."
                    status:
                        type: string
                        example: "success"
        400:
            description: "{matriz} não é uma empresa de rede, é individual."
            schema:
                properties:
                    content:
                        properties:
                            message:
                                type: string
                                example: "{matriz} não é a matriz da rede."
                    status:
                        type: string
                        example: "failure"

    """
    if not matriz:
        raise BadRequest("ID da academia não informado")
    
    bq_ = bq(id_empresa=matriz)
    gym_context = bq_.get_gym_context()
    instance_id = get_from_empresa(matriz)

    if not gym_context:
        return jsonify({
            "status": "failure",
            "content":{
                "message": f"a matriz {matriz} não foi encontrada.",
            }
        }), 400

    chaveMatriz = gym_context.get("chaveMatriz")
    if not chaveMatriz:
        return jsonify({
            "status": "failure",
            "content":{
                "message": f"{matriz} não é uma empresa de rede, é individual.",
            }
        }), 400
    
    if chaveMatriz != matriz:
        return jsonify({
            "status": "failure",
            "content":{
                "message": f"{matriz} não é a matriz da rede.",
            }
        }), 400
    
    bq_matriz = bq(id_empresa=chaveMatriz)
    chain_context = bq_matriz.get_chain_context()

    current_app.redis_client.delete(f"chain_context-{matriz}")
    current_app.redis_client.delete(f"is_rede:{instance_id}")
    try:
        for empresa in chain_context:
            filial_id = empresa.get("id_empresa")
            bq_ = bq(id_empresa=filial_id)
            if DATABASE == "bigquery":
                database = f"{GCP_BIGQUERY_DATASET}"
            else:
                database = f"{DB_SCHEMA}"
            bq_._execute_query_no_error(f"DELETE FROM {database}.contexto_academia", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.contexto_campanhas", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.contexto_fases", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.contexto_personalidade", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.contexto_planos", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.contexto_produtos", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.contexto_turmas", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.contexto_usuario", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.empresas", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.gymbot_departamentos", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.gymbot_tokens", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.memories", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.metas_diarias", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.notificacoes_schema", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.pacto_users", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.sessions", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.stickers", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.stickers_usuarios", use_id_empresa=True)
            
        empresa = chaveMatriz
        bq_matriz._execute_query_no_error(f"DELETE FROM {database}.instances", use_id_empresa=True)
        bq_matriz._execute_query_no_error(f"DELETE FROM {database}.redes", use_id_empresa=True)


        return jsonify({
            "status": "success",
            "content":{
                "message": f"Rede da matriz {empresa} apagada com sucesso.",
            }
        }), 200
    except Exception as e:
        logger.error(f"Erro ao apagar empresa {empresa} da rede: {e}")
        return jsonify({
            "status": "failure",
            "content":{
                "message": f"Erro ao apagar empresa {empresa} da rede: {e}",
            }
        }), 500

@rede_bp.route('/matriz/<matriz>/filial/<filial>', methods=['DELETE'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="rede",
    capture_body_fields=["matriz", "filial"],
    capture_query_params=["matriz", "filial"],
)
def apagar_matriz_rede_filial(matriz: str, filial: str):
    """
    Esta rota serve para apagar uma filial de uma rede por meio da chave-unidade.
    ---
    tags:
        - Rede
    parameters:
      - name: matriz
        in: path
        type: string
        default: chave-1
        required: true
        description: ID da academia matriz, no formato chave-id
      - name: filial
        in: path
        type: string
        default: chave-1
        required: true
        description: ID da academia filial, no formato chave-id
    security: 
        - Bearer: []
    responses:
        200:
            description: Retorna os membros de uma rede de empresas
            schema:
                properties:
                    content:
                        properties:
                            message:
                                type: string
                                example: "Rede da matriz {matriz} apagada com sucesso."
                    status:
                        type: string
                        example: "success"
        400:
            description: "{matriz} não é uma empresa de rede, é individual."
            schema:
                properties:
                    content:
                        properties:
                            message:
                                type: string
                                example: "{matriz} não é a matriz da rede."
                    status:
                        type: string
                        example: "failure"

    """
    if not matriz:
        raise BadRequest("ID da academia não informado")
    
    if not filial:
        raise BadRequest("ID da academia não informado")
    
    bq_ = bq(id_empresa=matriz)
    gym_context = bq_.get_gym_context()

    if not gym_context:
        return jsonify({
            "status": "failure",
            "content":{
                "message": f"a matriz {matriz} não foi encontrada.",
            }
        }), 400

    chaveMatriz = gym_context.get("chaveMatriz")
    if not chaveMatriz:
        return jsonify({
            "status": "failure",
            "content":{
                "message": f"{matriz} não é uma empresa de rede, é individual.",
            }
        }), 400
    
    if chaveMatriz == matriz:
        return jsonify({
            "status": "failure",
            "content":{
                "message": f"{matriz} é a matriz da rede. Nesse endpoint não é possível apagar a matriz.",
            }
        }), 400
    
    bq_matriz = bq(id_empresa=chaveMatriz)
    chain_context = bq_matriz.get_chain_context()


    try:
        for empresa in chain_context:
            if empresa.get("id_empresa") != filial: continue
            filial_id = empresa.get("id_empresa")
            bq_ = bq(id_empresa=filial_id)
            if DATABASE == "bigquery":
                database = f"{GCP_BIGQUERY_DATASET}"
            else:
                database = f"{DB_SCHEMA}"
            bq_._execute_query_no_error(f"DELETE FROM {database}.contexto_academia", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.contexto_campanhas", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.contexto_fases", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.contexto_personalidade", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.contexto_planos", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.contexto_produtos", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.contexto_turmas", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.contexto_usuario", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.empresas", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.gymbot_departamentos", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.gymbot_tokens", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.memories", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.metas_diarias", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.notificacoes_schema", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.pacto_users", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.sessions", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.stickers", use_id_empresa=True)
            bq_._execute_query_no_error(f"DELETE FROM {database}.stickers_usuarios", use_id_empresa=True)

        chain_context = [empresa for empresa in chain_context if empresa.get("id_empresa") != filial]
        bq_.save_chain_context(chain_context)
        return jsonify({
            "status": "success",
            "content":{
                "message": f"a filial {filial} da rede {matriz} apagada com sucesso.",
            }
        }), 200
    except Exception as e:
        logger.error(f"Erro ao apagar empresa {filial} da rede {matriz}: {e}")
        return jsonify({
            "status": "failure",
            "content":{
                "message": f"Erro ao apagar empresa {filial} da rede {matriz}: {e}",
            }
        }), 500


@rede_bp.route('/apagar_contexto_aluno', methods=['DELETE'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="rede",
    capture_body_fields=["matriz"],
    capture_query_params=["matriz"],
)
def apagar_contexto_aluno_rede(matriz: str = None):
    """
    Esta rota serve para apagar o contexto dos alunos da rede de academias, esse dado serve para que a IA saiba quem é o usuário e tenha seu número de telefone.
    ---
    tags:
        - Rede
    security: 
        - Bearer: []
    parameters:
      - name: matriz
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: telefone
        in: query
        type: string
        default: '+5562988887777'
        required: true
        description: Telefone do aluno
      - name: reset_chat
        in: query
        type: boolean
        default: false
        required: false
        description: Se True, apaga o histórico de mensagens do chat
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
        400:
            description: ID da academia ou telefone não informado
            schema:
                id: error
    """

    matriz = request.args.get('matriz', None)
    if not matriz:
        raise BadRequest("ID da matriz não informado")

    telefone = request.args.get('telefone', None)

    if not telefone:
        raise BadRequest("Telefone não informado")

    reset_chat = request.args.get('reset_chat', False)

    try:
        telefone = parse_phone(telefone)
    except (AttributeError, IndexError) as e:
        raise BadRequest("Telefone inválido") from e
    
    bq_matriz = bq(id_empresa=matriz)
    rede = bq_matriz.get_chain_context()

    if not rede:
        return jsonify({
            "status": "failure",
            "content":{
                "message": f"Rede para {matriz} não encontrada.",
            }
        }), 400
    
    if DATABASE == "bigquery":
        table_id = f"{GCP_BIGQUERY_DATASET}.contexto_usuario"
    elif DATABASE == "postgres":
        table_id = f"{DB_SCHEMA}.contexto_usuario"
    else:
        raise BadRequest("Erro ao apagar contexto de alunos")

    for empresa in rede:
        filial = empresa.get("id_empresa")
        query = f"DELETE FROM {table_id}"
        extra_query = f"AND telefone  = '{telefone}'"

        bq_filial = bq(id_empresa=filial)
        bq_filial._execute_query(query, extra_query=extra_query)
        current_app.redis_client.delete(f"{telefone}-{filial}")

        if reset_chat:
            current_app.redis_client.delete(f"{telefone}-waiting_cpf")
            current_app.redis_client.delete(f"is_client:{filial}:{telefone}")
            current_app.redis_client.delete(f"system_context:{filial}:{telefone}")
            current_app.redis_client.delete(f"save_empresa:{filial}-{telefone}")
            current_app.redis_client.delete(f"retry_contact:{telefone}-{filial}")
            current_app.redis_client.delete(f"pendency_verification:{telefone}-{filial}")
            current_app.redis_client.delete(f"current_conversation:{telefone}-{filial}")
            current_app.redis_client.delete(f"id_empresa-{telefone}-{filial}")
            current_app.redis_client.delete(f"CPF_search:{filial}:{telefone}")
            current_app.redis_client.delete(f"memories:{filial}:{telefone}")
            message_placeholder = [
                {
                    "enviado_por": "assistant",
                    "mensagem": "...",
                }
            ]
            current_app.redis_client.delete(f"last_messages-{telefone}-{filial}")
            current_app.redis_client.set(
                f"last_messages-{telefone}-{filial}",
                json.dumps(message_placeholder),
                ex=24*60*60
            )

    return jsonify({"success": "success"}), 200
