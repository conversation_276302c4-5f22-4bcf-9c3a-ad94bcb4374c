import json
from datetime import datetime
from uuid import uuid4

from src.api.app.tracing import inject
from src.data.bigquery_data import BigQueryData as bq
from src.extras.util import is_running, parse_phone, get_id_conversa
from src.extras.task_adapter import TaskAdapter


def processar_e_rotear_mensagem(
    task_inicial: dict,
    redis_client,
    logger,
    tracer=None,
    executar_pos_roteamento=None
):
    """
    Função centralizada para validar, enriquecer, e rotear uma mensagem recebida.
    Assume que todas as mensagens devem ser enriquecidas com metadados padrão.

    Args:
        task_inicial (dict): O dicionário inicial da tarefa, vindo do webhook.
        redis_client: A instância do cliente Redis.
        logger: A instância do logger.
        tracer: (Opcional) A instância do tracer para OpenTelemetry.
        executar_pos_roteamento (function): (Opcional) Uma função a ser executada após
                                            a mensagem ser enviada para a fila.
    """
    try:
        id_empresa = task_inicial.get("id_empresa")
        origin = task_inicial.get("origin")

        # 1. Adaptar e validar a task inicial com TaskAdapter
        task, msg = TaskAdapter(task_inicial, origin, id_empresa).new_task
        if not task:
            logger.warning(f"TaskAdapter retornou nulo. Mensagem: {msg}")
            return {"failure": msg}, 400

        logger.info("Adicionando metadados e contexto compartilhados.")
        task["task_uid"] = f"receber_mensagem_{str(uuid4())[:8]}"
        task["momento_recebimento"] = task_inicial.get(
            "momento_recebimento", datetime.now().timestamp()
        )

        phone_number = parse_phone(task.get("data", {}).get("phone", ""))

        # Neste momento, é criado um id novo, caso não exista
        id_conversa, info = get_id_conversa(
            phone_number, id_empresa, return_info=True
        )

        task["new_conversation"] = info.get("new", False)

        logger.info(f"\n\nTask enriquecida: {json.dumps(task, indent=2)}")

        # 3. Obter informações para roteamento
        bq_ = bq(id_empresa=id_empresa)
        model_source = bq_.get_model_source()
        is_routerllm_running = is_running(
            "src.routerllm.messages_received_worker"
        )

        logger.info(f"\nModel source: {model_source}")
        logger.info(f"Is running routerllm: {is_routerllm_running}\n")

        # 4. Injetar contexto de tracing (se aplicável)
        if tracer:
            with tracer.start_as_current_span("send_message_to_worker") as span:
                span.set_attribute("id_empresa", id_empresa)
                span.set_attribute("phone", phone_number) # Usando o número já parseado
                span.set_attribute("model_source", str(model_source))
                span.set_attribute("origin", origin)
                carrier = {}
                inject(carrier)
                task["context"] = carrier

        task["tempo_processamento_meta"] = {}
        momento_recebimento = task.get("momento_recebimento")
        task["tempo_processamento_meta"]["tempo_processamento_inicial"] = datetime.now().timestamp() - momento_recebimento

        # 5. Roteamento para a fila Redis
        if str(model_source) == "routerllm" and is_routerllm_running:
            logger.info("Enviando mensagem para o routerllm")
            redis_client.lpush('messages_received_routerllm', json.dumps(task))
        else:
            logger.info("Enviando mensagem para o worker")
            redis_client.lpush('messages_received', json.dumps(task))

        # 6. Executar ações pós-roteamento (se houver)
        if executar_pos_roteamento:
            # Passamos a task final e o id_empresa para a função de callback
            executar_pos_roteamento(task, id_empresa)

        return {"success": "success"}, 200

    except Exception as e:
        logger.error(f"Erro em processar_e_rotear_mensagem: {e}", exc_info=True)
        return {"error": str(e)}, 500
