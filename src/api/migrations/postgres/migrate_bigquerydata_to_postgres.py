import os
import json
import logging
from datetime import datetime
from psycopg2 import sql, connect
from psycopg2.extras import execute_values

from google.cloud import bigquery
from google.oauth2 import service_account

logger = logging.getLogger("conversas_logger")

current_dir = os.path.dirname(__file__)
gbq_credentials_file_path = os.path.join(current_dir,'conversas-ai.json')

with open(gbq_credentials_file_path, "r") as file:
    CREDENTIALS = json.load(file)


class Connections:
    def __init__(self) -> None:
        credentials = service_account.Credentials.from_service_account_info(CREDENTIALS)
        self.bigquery_client: bigquery.Client = bigquery.Client(credentials=credentials, project=CREDENTIALS['project_id'])
        logger.info("BigQuery client loaded!")
        logger.info("Google Storage client loaded!")
        self.postgres_connection = connect(
            dbname=os.getenv("POSTGRES_DB"),
            user=os.getenv("POSTGRES_USER"),
            password=os.getenv("POSTGRES_PASSWORD"),
            host=os.getenv("POSTGRES_HOST"),
            port=os.getenv("POSTGRES_PORT", 5432)
        )
        logger.info("Postgres client loaded!")


def get_postgres_columns(cursor, schema, table_name) -> set:
    query = sql.SQL("""
        SELECT column_name FROM information_schema.columns
        WHERE table_schema = %s AND table_name = %s;
    """)
    cursor.execute(query, (schema, table_name))
    return {row[0] for row in cursor.fetchall()}

def transform_batch(batch: list, pg_columns: set) -> list:
    transformed = []
    for row in batch:
        new_row = {}
        for key, value in row.items():
            if key not in pg_columns:
                continue

            if isinstance(value, datetime):
                if key in {
                    "data_ultima_atualizacao",
                    "movproduto_datalancamento",
                    "data_analise",
                    "data_requisicao",
                    "data_envio",
                    "date",
                    "data_inicio",
                    "data_fim"
                }:
                    new_row[key] = value
                else:
                    new_row[key] = value.isoformat()

            elif isinstance(value, (dict, list)):
                new_row[key] = json.dumps(value)

            elif isinstance(value, str):
                cleaned_value = value.replace('\x00', '')
                new_row[key] = cleaned_value if cleaned_value else None

            else:
                new_row[key] = value
                
        transformed.append(new_row)
    return transformed

def run_migration():
    connections = Connections()
    bq_client = connections.bigquery_client
    pg_conn = connections.postgres_connection
    
    GCP_BIGQUERY_DATASET = os.getenv("GCP_BIGQUERY_DATASET")
    DB_SCHEMA = os.getenv("DB_SCHEMA", "public")

    MIGRATE_LIMIT = os.getenv("MIGRATE_LIMIT")  # Limite para a query no BigQuery
    DO_TRUNCATE = os.getenv("MIGRATION_DO_TRUNCATE", "false").lower() == "true"

    TABLES_TO_MIGRATE = [
        "api_keys", 
        "configuration",
        "contexto_academia",
        "contexto_campanhas",
        "contexto_fases",
        "contexto_personalidade",
        "contexto_planos",
        "contexto_produtos",
        "contexto_turmas",
        "contexto_usuario",
        "conversas",
        "conversas_analise",
        "datas_execucoes",
        "empresas",
        "gymbot_departamentos",
        "gymbot_tokens",
        "instances",
        "logs_conexao_whatsapp",
        "logs_erros_terminais",
        "logs_mensagens_enviadas",
        "logs_requests_conversas_ai",
        "logs_requests_gymbot",
        "logs_requests_sistema_pacto",
        "logs_requests_zapi",
        "memories",
        "metas_diarias",
        "notificacoes_schema",
        "pacto_users",
        "redes",
        "sessions",
        "status_envios",
        "stickers",
        "strikes_usuarios",
        "vendas_conversas",
    ]

    def extract_from_bigquery(table_name, batch_size=500, limit=None):
        logger.info(f"Extraindo dados da tabela do BigQuery: {table_name}...")
        try:
            query = f"SELECT * FROM `{GCP_BIGQUERY_DATASET}.{table_name}`"
            if limit:
                logger.warning(f"Aplicando um limite de {limit} registros para a tabela {table_name}.")
                query += f" LIMIT {int(limit)}"
        
            rows_iterator = bq_client.query(query).result(page_size=batch_size)
            for page in rows_iterator.pages:
                batch = [dict(row) for row in page]
                if batch:
                    yield batch
            logger.info(f"Extração da tabela {table_name} concluída.")
        except Exception as e:
            logger.error(f"Falha ao extrair dados da tabela {table_name} do BigQuery: {e}")
            raise # levantando este erro pra interromper a migração dessa tabela

    def load_to_postgres(target_table, data_batch):
        if not data_batch:
            return
        with pg_conn.cursor() as cursor:
            columns = data_batch[0].keys()
            cols_sql = sql.SQL(", ").join(map(sql.Identifier, columns))
            insert_query = sql.SQL("INSERT INTO {table} ({cols}) VALUES %s").format(
                table=sql.Identifier(DB_SCHEMA, target_table),
                cols=cols_sql
            )
            values_to_insert = [[row.get(col) for col in columns] for row in data_batch]
            execute_values(cursor, insert_query, values_to_insert) # commit será feito no final do processo da tabela

    logger.info("Iniciando o processo de migração...")
    if MIGRATE_LIMIT:
        logger.warning(f"MODO DE TESTE ATIVADO: A migração será limitada a {MIGRATE_LIMIT} registros por tabela.")
    if DO_TRUNCATE:
        logger.info("Estratégia: Truncate and Load (Limpar e Carregar).")
    else:
        logger.info("Estratégia: Apenas Inserir (Append-Only).")

    try:
        for table_name in TABLES_TO_MIGRATE:
            try:
                logger.info(f"--- Migrando tabela: {table_name} ---")
                
                if DO_TRUNCATE:
                    logger.info(f"Limpando a tabela de destino '{table_name}' no PostgreSQL...")
                    with pg_conn.cursor() as cursor:
                        truncate_command = sql.SQL("TRUNCATE TABLE {} RESTART IDENTITY CASCADE").format(
                            sql.Identifier(DB_SCHEMA, table_name)
                        )
                        cursor.execute(truncate_command)
                    logger.info(f"Tabela '{table_name}' limpa com sucesso.")
                
                with pg_conn.cursor() as cursor:
                    pg_columns = get_postgres_columns(cursor, DB_SCHEMA, table_name)
                
                if not pg_columns:
                    logger.warning(f"Tabela de destino '{table_name}' não encontrada. Pulando.")
                    continue

                data_generator = extract_from_bigquery(table_name, limit=MIGRATE_LIMIT)
                for batch in data_generator:
                    transformed = transform_batch(batch, pg_columns)
                    load_to_postgres(table_name, transformed)

                pg_conn.commit()
                logger.info(f"--- Tabela '{table_name}' migrada com sucesso. ---")
            except Exception as e:
                logger.error(f"!!! Erro ao migrar a tabela {table_name}. Pulando. Erro: {e}")
                pg_conn.rollback()

    finally:
        if pg_conn:
            pg_conn.close()
            logger.info("Conexão com o PostgreSQL fechada.")

    logger.info("Processo de migração finalizado.")


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    run_migration()
