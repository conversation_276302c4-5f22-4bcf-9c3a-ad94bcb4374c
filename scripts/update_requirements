#!/bin/bash
set -e

# This script updates requirements.txt from requirements.in using pip-compile in a Docker container.
# It ensures a clean, reproducible environment for dependency resolution.
IMAGE_TAG=tmp-pip-compile
CONTAINER_NAME=pip-compile-worker

clean () {
    echo "Cleaning up container and image..."
    docker rm --force $CONTAINER_NAME 2>/dev/null || true
    docker rmi $IMAGE_TAG 2>/dev/null || true
}

clean

echo "Pulling base Python image..."
docker pull python:3.10-slim-buster

echo "Tagging image as $IMAGE_TAG..."
docker image tag python:3.10-slim-buster $IMAGE_TAG

echo "Creating temporary container $CONTAINER_NAME..."
docker create --name $CONTAINER_NAME $IMAGE_TAG tail -f /dev/null

echo "Copying requirements.in into container..."
docker cp ./requirements.in $CONTAINER_NAME:/requirements.in

echo "Installing pip-tools and running pip-compile in container..."
docker start $CONTAINER_NAME
docker exec $CONTAINER_NAME sh -c "pip install pip-tools && pip-compile --upgrade --generate-hashes requirements.in"

echo "Copying generated requirements.txt back to host..."
docker cp $CONTAINER_NAME:/requirements.txt ./requirements.txt

clean

echo "Done."
