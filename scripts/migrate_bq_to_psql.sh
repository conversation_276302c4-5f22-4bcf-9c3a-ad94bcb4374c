#!/bin/bash

set -e
set -o pipefail

COLOR_GREEN='\033[0;32m'
COLOR_RED='\033[0;31m'
COLOR_YELLOW='\033[1;33m'
COLOR_BLUE='\033[0;34m'
COLOR_RESET='\033[0m'

log_info() { echo -e "${COLOR_BLUE}INFO:${COLOR_RESET} $1"; }
log_success() { echo -e "${COLOR_GREEN}SUCESSO:${COLOR_RESET} $1"; }
log_error() { echo -e "${COLOR_RED}ERRO:${COLOR_RESET} $1"; }
log_warn() { echo -e "${COLOR_YELLOW}AVISO:${COLOR_RESET} $1"; }

PYTHON_SCRIPT_PATH="src/api/migrations/postgres/migrate_bigquerydata_to_postgres.py"

MIGRATE_LIMIT=""
MIGRATION_DO_TRUNCATE="false"

show_help() {
    echo "Uso: $0 [opções]"
    echo
    echo "Opções:"
    echo "  --limit <N>      <PERSON>ita a extração do BigQuery a N registros por tabela (para testes)."
    echo "  --truncate       Ativa a limpeza (TRUNCATE) das tabelas antes da migração."
    echo "  -h, --help       Mostra esta ajuda."
}

while [[ "$#" -gt 0 ]]; do
    case $1 in
        --limit)
            MIGRATE_LIMIT="$2"
            shift # passa para o próximo argumento
            ;;
        --truncate)
            MIGRATION_DO_TRUNCATE="true"
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "Opção desconhecida: $1"
            show_help
            exit 1
            ;;
    esac
    shift # passa para a próxima opção
done

main() {
    log_info "Iniciando script de migração local..."
    if [ ! -z "$MIGRATE_LIMIT" ]; then
        if ! [[ "$MIGRATE_LIMIT" =~ ^[1-9][0-9]*$ ]]; then
            log_error "MIGRATE_LIMIT deve ser um número inteiro positivo. Valor atual: '$MIGRATE_LIMIT'"
            exit 1
        fi
        log_info "MIGRATE_LIMIT definido como: $MIGRATE_LIMIT"
        log_warn "Executando em modo de teste com LIMIT=$MIGRATE_LIMIT."
    fi
    if [ "$MIGRATION_DO_TRUNCATE" = "true" ]; then
        log_warn "A limpeza de tabelas (TRUNCATE) está ATIVADA."
    else
        log_info "A limpeza de tabelas (TRUNCATE) está DESATIVADA (modo Apenas Inserir)."
    fi
    
    echo "--------------------------------------------------"
    
    PYTHONPATH=. python3 "$PYTHON_SCRIPT_PATH"
    
    echo "--------------------------------------------------"
    log_success "Script de migração finalizado com sucesso!"
}

main