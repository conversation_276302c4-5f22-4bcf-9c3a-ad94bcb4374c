#!/bin/bash

# Função para verificar se o comando jq está instalado
command -v jq >/dev/null 2>&1 || { echo >&2 "O comando 'jq' é necessário mas não está instalado. Instale-o com 'sudo apt install jq' ou equivalente."; exit 1; }

# Parâmetros para autenticação
AUTH_URL="https://api.conversas.ai/auth/"
API_KEY="api-key"
EMPRESA="empresa-id"

# Array com os números de telefone no formato internacional
phones=(
    "+5562998680154"
    "+5564999375581"
    "+5562983176495"
    "+5562984340200"
    "+5562995609063"
    "+5562992113941"
)

# Parâmetros comuns da requisição DELETE
BASE_URL="https://api.conversas.ai/apagar_contexto_aluno/"

# Realiza a autenticação para obter o auth_token
echo "Realizando autenticação..."
AUTH_RESPONSE=$(curl -s -X POST "${AUTH_URL}" \
    -H "accept: application/json" \
    -H "Content-Type: application/json" \
    -d "{\"api_key\": \"${API_KEY}\"}")

# Extrai o auth_token da resposta usando jq
AUTH_TOKEN=$(echo "${AUTH_RESPONSE}" | jq -r '.auth_token')

# Verifica se o token foi obtido com sucesso
if [ -z "${AUTH_TOKEN}" ] || [ "${AUTH_TOKEN}" == "null" ]; then
    echo "Erro: Não foi possível obter o auth_token. Resposta: ${AUTH_RESPONSE}"
    exit 1
else
    echo "Autenticação bem-sucedida. Token obtido."
fi

# Loop para executar a requisição cURL para cada número
for phone in "${phones[@]}"; do
    # Codifica o símbolo '+' para '%2B' na URL
    encoded_phone=$(echo "$phone" | sed 's/+/%2B/')
    echo "Executando requisição para o telefone: $phone"
    
    # Executa a requisição cURL
    curl -X DELETE "${BASE_URL}?empresa=${EMPRESA}&telefone=${encoded_phone}&reset_chat=true" \
         -H "accept: application/json" \
         -H "Authorization: ${AUTH_TOKEN}"
    
    # Verifica o status da requisição
    if [ $? -eq 0 ]; then
        echo "Requisição para $phone concluída com sucesso."
    else
        echo "Erro ao executar requisição para $phone."
    fi
    echo "----------------------------------------"
done

echo "Todas as requisições foram processadas."