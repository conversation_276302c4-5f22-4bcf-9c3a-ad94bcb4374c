FROM python:3.10-slim-bookworm AS dependencies

COPY requirements-migrate.txt .
RUN pip install -r requirements-migrate.txt

FROM dependencies

WORKDIR /app

ENV TZ=${TZ:-"America/Sao_Paulo"}
RUN apt-get update && apt-get install -y curl && apt-get clean

ENV GCP_BIGQUERY_DATASET=${GCP_BIGQUERY_DATASET:-"development"}
ENV GOOGLE_APPLICATION_CREDENTIALS=${GOOGLE_APPLICATION_CREDENTIALS:-"src/connections/conversas-ai.json"}
ENV POSTGRES_HOST=${POSTGRES_HOST:-"postgres"}
ENV POSTGRES_PORT=${POSTGRES_PORT:-"5432"}
ENV POSTGRES_DB=${POSTGRES_DB:-"conversas_ai"}
ENV POSTGRES_USER=${POSTGRES_USER:-"postgres"}
ENV POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-"postgres"}
ENV DB_SCHEMA=${DB_SCHEMA:-"public"}
ENV MIGRATE_LIMIT=${MIGRATE_LIMIT:-"100"}
ENV MIGRATION_DO_TRUNCATE=${MIGRATION_DO_TRUNCATE:-"true"}

COPY ./src/api/migrations/postgres/migrate_bigquerydata_to_postgres.py /app/migrate_bigquerydata_to_postgres.py
COPY ./src/connections/conversas-ai.json /app

ENV PYTHONPATH=/app:$PYTHONPATH

CMD ["python", "/app/migrate_bigquerydata_to_postgres.py"]
