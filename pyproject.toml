[project]
name = "orion"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "aiohttp==3.11.16",
    "apscheduler==3.11.0",
    "bs4==0.0.2",
    "coverage==7.8.0",
    "debugpy==1.8.14",
    "firebase-admin==6.7.0",
    "flasgger==*******",
    "flask==3.1.0",
    "flask-limiter==3.11.0",
    "flask-login==0.6.3",
    "flask-redis==0.4.0",
    "flask-restful==0.3.10",
    "google-api-python-client==2.167.0",
    "google-auth==2.39.0",
    "google-auth-httplib2==0.2.0",
    "google-auth-oauthlib==1.2.1",
    "google-cloud-bigquery-storage==2.30.0",
    "google-cloud-bigquery[pandas]==3.31.0",
    "google-cloud-storage==3.1.0",
    "google-generativeai==0.8.4",
    "instructor==1.7.9",
    "langchain-core==0.3.56",
    "llm-flow-test",
    "oauthlib==3.2.2",
    "openai==1.71.0",
    "opentelemetry-api==1.32.1",
    "opentelemetry-exporter-jaeger==1.21.0",
    "opentelemetry-instrumentation==0.53b1",
    "opentelemetry-sdk==1.32.1",
    "pandas==2.2.3",
    "psycopg2-binary==2.9.10",
    "pydub==0.25.1",
    "pyjwt==2.10.1",
    "pyopenssl==25.0.0",
    "pytest==8.3.5",
    "pytest-mock==3.14.1",
    "qdrant-client==1.14.2",
    "redis==5.2.1",
    "requests==2.32.3",
    "rq==2.3.2",
    "rq-scheduler==0.14.0",
    "thefuzz==0.22.1",
    "together==1.5.5",
    "unidecode==1.3.8",
    "uuid==1.30",
    "waitress==3.0.2",
    "watchdog==6.0.0",
]

[tool.uv.workspace]
members = [
    "tests/integration",
]

[tool.uv.sources]
llm-flow-test = { workspace = true }
